<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com; font-src 'self' https://cdnjs.cloudflare.com; img-src 'self' data:;">
    <title>X-Stein Admin Panel</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div id="app">


        <!-- Global AI Settings Button -->
        <button id="globalAiSettingsBtn" class="global-ai-btn" title="AI Settings">
            <i class="fas fa-brain"></i>
        </button>

        <!-- Global AI Settings Sidebar -->
        <div id="globalAiSidebar" class="global-ai-sidebar">
            <div class="ai-sidebar-header">
                <h3 data-translate="aiSettings"><i class="fas fa-brain"></i> AI Settings</h3>
                <button id="closeAiSidebar" class="close-sidebar-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- Scroll Progress Indicator -->
            <div class="ai-sidebar-scroll-progress"></div>

            <!-- Scroll to Top Button -->
            <button id="aiSidebarScrollTop" class="ai-sidebar-scroll-indicator" title="Scroll to top">
                <i class="fas fa-chevron-up"></i>
            </button>

            <div class="ai-sidebar-content">
                <!-- Question Count Settings removed - now managed through Rate Limits in admin panel -->

                <!-- AI Agent Integration -->
                <div class="sidebar-card gemini-settings-card">
                    <div class="card-header">
                        <div class="header-icon">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="header-content">
                            <h3 class="card-title">AI Agent</h3>
                            <p class="card-subtitle">Enhanced AI question generation</p>
                        </div>
                    </div>

                    <div class="card-body">
                        <div id="geminiAuthStatus" class="gemini-auth-status">
                            <div class="auth-status-indicator">
                                <i class="fas fa-circle status-icon disconnected" id="geminiStatusIcon"></i>
                                <span class="status-text" id="geminiStatusText">Connect AI Agent to get started</span>
                            </div>
                        </div>

                        <!-- Account Switcher (shown when authenticated) -->
                        <div id="accountSwitcher" class="account-switcher" style="display: none;">
                            <div class="account-dropdown-container">
                                <div class="account-dropdown-header">
                                    <i class="fas fa-user-circle"></i>
                                    <span>Account</span>
                                </div>
                                <div class="account-dropdown">
                                    <button id="accountDropdownBtn" class="account-dropdown-btn">
                                        <div class="account-info">
                                            <span class="account-name" id="currentAccountName">Loading...</span>
                                            <span class="account-email" id="currentAccountEmail">Loading...</span>
                                        </div>
                                        <i class="fas fa-chevron-down dropdown-arrow"></i>
                                    </button>
                                    <div id="accountDropdownMenu" class="account-dropdown-menu">
                                        <div class="dropdown-header">
                                            <span>Switch Account</span>
                                        </div>
                                        <div id="accountList" class="account-list">
                                            <!-- Accounts will be populated here -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="geminiAuthActions" class="gemini-auth-actions">
                            <button id="geminiSignInBtn" class="sidebar-btn btn-primary">
                                <div class="btn-icon">
                                    <i class="fas fa-sign-in-alt"></i>
                                </div>
                                <div class="btn-content">
                                    <span class="btn-title">Sign In to AI Agent</span>
                                    <span class="btn-subtitle">Free access with Google account</span>
                                </div>
                            </button>

                            <button id="geminiSignOutBtn" class="sidebar-btn btn-secondary" style="display: none;">
                                <div class="btn-icon">
                                    <i class="fas fa-sign-out-alt"></i>
                                </div>
                                <div class="btn-content">
                                    <span class="btn-title">Sign Out</span>
                                    <span class="btn-subtitle">Disconnect from AI Agent</span>
                                </div>
                            </button>
                        </div>

                        <div id="geminiFeatures" class="gemini-features" style="display: none;">
                            <div class="feature-list">
                                <div class="feature-item">
                                    <i class="fas fa-check-circle"></i>
                                    <span>Enhanced question quality</span>
                                </div>
                                <div class="feature-item">
                                    <i class="fas fa-check-circle"></i>
                                    <span>Multi-format content processing</span>
                                </div>
                                <div class="feature-item">
                                    <i class="fas fa-check-circle"></i>
                                    <span>Advanced explanations</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- AI Model Selection -->
                <div class="sidebar-card model-selection-card">
                    <div class="card-header">
                        <div class="header-icon">
                            <i class="fas fa-brain"></i>
                        </div>
                        <div class="header-content">
                            <h3 class="card-title" data-translate="aiModelSelection">AI Model Selection</h3>
                            <p class="card-subtitle" data-translate="aiModelSelectionDesc">Choose your preferred AI model for question generation</p>
                        </div>
                    </div>

                    <div class="card-body">
                        <div class="model-selector-container">
                            <div class="selector-wrapper">
                                <label for="globalModelSelect" class="selector-label">
                                    <i class="fas fa-robot"></i>
                                    <span data-translate="preferredAiModel">Preferred AI Model</span>
                                </label>
                                <div class="custom-select-wrapper">
                                    <select id="globalModelSelect" class="modern-select">
                                        <option value="auto" data-translate="autoBestAvailable">Auto (Best Available)</option>
                                        <!-- Models will be loaded dynamically from backend -->
                                    </select>
                                    <div class="select-arrow">
                                        <i class="fas fa-chevron-down"></i>
                                    </div>
                                </div>
                            </div>

                            <div class="model-status-card" id="globalModelStatus">
                                <div class="status-indicator-wrapper">
                                    <span class="status-indicator status-unknown"></span>
                                    <span class="status-text" data-translate="checkingAvailability">Checking availability...</span>
                                </div>
                                <div class="status-details">
                                    <span class="status-description" data-translate="modelStatusWillAppear">Model status will appear here</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Model Management -->
                <div class="sidebar-card model-management-card">
                    <div class="card-header">
                        <div class="header-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <div class="header-content">
                            <h3 class="card-title" data-translate="modelManagement">Model Management</h3>
                            <p class="card-subtitle" data-translate="modelManagementDesc">Manage your AI models and API configuration</p>
                        </div>
                    </div>

                    <div class="card-body">
                        <div class="management-grid-sidebar">
                            <div class="management-section">
                                <h4 class="section-title">
                                    <i class="fas fa-robot"></i>
                                    <span data-translate="aiModels">AI Models</span>
                                </h4>
                                <div class="action-buttons-grid-sidebar">
                                    <button id="globalAddModelBtn" class="sidebar-btn btn-add">
                                        <div class="btn-icon">
                                            <i class="fas fa-plus"></i>
                                        </div>
                                        <div class="btn-content">
                                            <span class="btn-title" data-translate="addModel">Add Model</span>
                                            <span class="btn-subtitle" data-translate="addNewAiModel">Add new AI model</span>
                                        </div>
                                    </button>

                                    <button id="globalRemoveModelBtn" class="sidebar-btn btn-remove">
                                        <div class="btn-icon">
                                            <i class="fas fa-trash"></i>
                                        </div>
                                        <div class="btn-content">
                                            <span class="btn-title" data-translate="removeModel">Remove Model</span>
                                            <span class="btn-subtitle" data-translate="deleteExistingModel">Delete existing model</span>
                                        </div>
                                    </button>
                                </div>
                            </div>

                            <div class="management-section">
                                <h4 class="section-title">
                                    <i class="fas fa-tools"></i>
                                    <span data-translate="toolsTesting">Tools & Testing</span>
                                </h4>
                                <div class="action-buttons-grid-sidebar">
                                    <button id="globalTestModelsBtn" class="sidebar-btn btn-test">
                                        <div class="btn-icon">
                                            <i class="fas fa-flask"></i>
                                        </div>
                                        <div class="btn-content">
                                            <span class="btn-title" data-translate="testModels">Test Models</span>
                                            <span class="btn-subtitle" data-translate="verifyFunctionality">Verify functionality</span>
                                        </div>
                                    </button>

                                    <button id="globalManageApiKeyBtn" class="sidebar-btn btn-api">
                                        <div class="btn-icon">
                                            <i class="fas fa-key"></i>
                                        </div>
                                        <div class="btn-content">
                                            <span class="btn-title" data-translate="apiKey">API Key</span>
                                            <span class="btn-subtitle" data-translate="manageCredentials">Manage credentials</span>
                                        </div>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Telegram Bot Settings -->
                <div class="sidebar-card telegram-settings-card">
                    <div class="card-header">
                        <div class="header-icon">
                            <i class="fab fa-telegram-plane"></i>
                        </div>
                        <div class="header-content">
                            <h3 class="card-title">Telegram Bot Settings</h3>
                            <p class="card-subtitle">Configure bot token and admin settings</p>
                        </div>
                    </div>

                    <div class="card-body">
                        <div class="settings-grid-sidebar">
                            <div class="setting-item">
                                <div class="setting-info">
                                    <div class="setting-icon">
                                        <i class="fas fa-key"></i>
                                    </div>
                                    <div class="setting-details">
                                        <span class="setting-label">Bot Token</span>
                                        <span class="setting-description">Telegram bot authentication token</span>
                                    </div>
                                </div>
                                <div class="setting-control">
                                    <input type="password" id="sidebarBotToken" class="setting-input" placeholder="Enter bot token">
                                </div>
                            </div>

                            <div class="setting-item">
                                <div class="setting-info">
                                    <div class="setting-icon">
                                        <i class="fas fa-user-shield"></i>
                                    </div>
                                    <div class="setting-details">
                                        <span class="setting-label">Admin IDs</span>
                                        <span class="setting-description">Comma-separated admin user IDs</span>
                                    </div>
                                </div>
                                <div class="setting-control">
                                    <input type="text" id="sidebarAdminIds" class="setting-input" placeholder="123456789,987654321">
                                </div>
                            </div>

                            <div class="setting-item">
                                <div class="setting-info">
                                    <div class="setting-icon">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <div class="setting-details">
                                        <span class="setting-label">Required Channel</span>
                                        <span class="setting-description">Channel users must join (optional)</span>
                                    </div>
                                </div>
                                <div class="setting-control">
                                    <input type="text" id="sidebarChannelId" class="setting-input" placeholder="-1001234567890">
                                </div>
                            </div>
                        </div>

                        <div class="action-buttons-grid-sidebar">
                            <button id="sidebarSaveBotConfig" class="sidebar-btn btn-primary">
                                <div class="btn-icon">
                                    <i class="fas fa-save"></i>
                                </div>
                                <div class="btn-content">
                                    <span class="btn-title">Save Configuration</span>
                                    <span class="btn-subtitle">Update bot settings</span>
                                </div>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Home Screen (Admin Dashboard) -->
        <div id="homeScreen" class="screen active">


            <!-- Left Sidebar Navigation -->
            <nav class="admin-sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <img src="../../assets/icons/X-Stein.png" alt="X-Stein" class="sidebar-logo-img">
                    <div class="sidebar-title">
                        <h2>X-Stein</h2>
                        <p>Admin Panel</p>
                    </div>
                </div>
            </div>

            <div class="sidebar-menu">
                <div class="menu-item active" data-section="bot-management">
                    <div class="menu-icon">
                        <i class="fab fa-telegram-plane"></i>
                    </div>
                    <div class="menu-content">
                        <span class="menu-title">Bot Management</span>
                        <span class="menu-subtitle">Complete bot administration dashboard</span>
                    </div>
                </div>

                <div class="menu-item" data-section="user-statistics">
                    <div class="menu-icon">
                        <i class="fas fa-users-cog"></i>
                    </div>
                    <div class="menu-content">
                        <span class="menu-title">User Management</span>
                        <span class="menu-subtitle">Manage users and subscriptions</span>
                    </div>
                </div>

                <div class="menu-item" data-section="rate-limits">
                    <div class="menu-icon">
                        <i class="fas fa-hourglass-half"></i>
                    </div>
                    <div class="menu-content">
                        <span class="menu-title">Rate Limits</span>
                        <span class="menu-subtitle">Configure user limits and cooldowns</span>
                    </div>
                </div>
            </div>

            <!-- Bot Control Buttons -->
            <div class="sidebar-bot-control">
                <div class="bot-status-mini">
                    <div class="status-indicator-mini">
                        <i class="fas fa-circle status-icon-mini" id="botStatusIconMini"></i>
                        <span class="status-text-mini" id="botStatusTextMini">Checking...</span>
                    </div>
                </div>
                <div class="bot-control-buttons">
                    <button id="startBotBtnSidebar" class="sidebar-btn btn-start">
                        <i class="fas fa-play"></i>
                        <span>Start Bot</span>
                    </button>
                    <button id="stopBotBtnSidebar" class="sidebar-btn btn-stop">
                        <i class="fas fa-stop"></i>
                        <span>Stop Bot</span>
                    </button>
                </div>

                <div class="bot-uptime-mini">
                    <div class="uptime-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="uptime-info">
                        <span class="uptime-value" id="botUptimeValueSidebar">0h 0m</span>
                        <span class="uptime-label">Uptime</span>
                    </div>
                </div>
            </div>

            <!-- User Statistics in Sidebar -->
            <div class="sidebar-user-stats">
                <div class="sidebar-stat-item">
                    <div class="sidebar-stat-icon total-users">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="sidebar-stat-info">
                        <span class="sidebar-stat-number" id="totalUsersCountSidebar">0</span>
                        <span class="sidebar-stat-label">Total Users</span>
                    </div>
                </div>

                <div class="sidebar-stat-item">
                    <div class="sidebar-stat-icon free-users">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="sidebar-stat-info">
                        <span class="sidebar-stat-number" id="freeUsersCountSidebar">0</span>
                        <span class="sidebar-stat-label">Free Users</span>
                    </div>
                </div>

                <div class="sidebar-stat-item">
                    <div class="sidebar-stat-icon paid-users">
                        <i class="fas fa-crown"></i>
                    </div>
                    <div class="sidebar-stat-info">
                        <span class="sidebar-stat-number" id="paidUsersCountSidebar">0</span>
                        <span class="sidebar-stat-label">Paid Users</span>
                    </div>
                </div>
            </div>

            <!-- Sidebar Stats -->
            <div class="sidebar-stats">
                <div class="sidebar-stat-item">
                    <div class="sidebar-stat-icon">
                        <i class="fas fa-question-circle"></i>
                    </div>
                    <div class="sidebar-stat-info">
                        <span class="sidebar-stat-number" id="questionsGenerated">0</span>
                        <span class="sidebar-stat-label">Questions Generated</span>
                    </div>
                </div>

                <div class="sidebar-stat-item">
                    <div class="sidebar-stat-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="sidebar-stat-info">
                        <span class="sidebar-stat-number" id="filesProcessed">0</span>
                        <span class="sidebar-stat-label">Files Processed</span>
                    </div>
                </div>


            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Unified Bot Management Section -->
            <div id="bot-management" class="admin-section active">
                <div class="section-header">
                    <h1>Telegram Bot Management</h1>
                    <p>Complete administration dashboard for your Telegram bot</p>
                </div>

                <div class="dashboard-grid dashboard-grid-1col">

                    <!-- Bot Configuration -->
                    <div class="dashboard-card config-card">
                        <div class="card-header">
                            <div class="card-title">
                                <i class="fas fa-cog"></i>
                                <h3>Bot Configuration</h3>
                            </div>
                            <div class="config-status" id="configStatus">
                                <span class="status-indicator" id="configStatusIndicator">
                                    <i class="fas fa-circle"></i>
                                </span>
                                <span id="configStatusText">Not Configured</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- Configuration Status Alert -->
                            <div class="config-alert" id="configAlert" style="display: none;">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i>
                                    <div class="alert-content">
                                        <strong>Configuration Required</strong>
                                        <p>Please configure your bot settings below to start using the Telegram bot.</p>
                                    </div>
                                </div>
                            </div>

                            <!-- First Row: Bot Token and Admin IDs -->
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="botTokenInput">
                                        <i class="fas fa-key"></i>
                                        Bot Token
                                        <span class="required">*</span>
                                    </label>
                                    <div class="input-group">
                                        <input type="password" id="botTokenInput" class="form-input" placeholder="Enter Telegram bot token">
                                        <button type="button" class="input-toggle-btn" id="toggleBotToken">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <small class="form-help">Get your bot token from @BotFather on Telegram</small>
                                </div>

                                <div class="form-group">
                                    <label for="adminIdsInput">
                                        <i class="fas fa-users-cog"></i>
                                        Admin IDs (comma-separated)
                                        <span class="required">*</span>
                                    </label>
                                    <input type="text" id="adminIdsInput" class="form-input" placeholder="123456789,987654321">
                                    <small class="form-help">Your Telegram user ID(s). Get it from @userinfobot</small>
                                </div>
                            </div>

                            <!-- Second Row: Channel ID and Admin Name -->
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="channelIdInput">
                                        <i class="fas fa-hashtag"></i>
                                        Required Channel ID (optional)
                                    </label>
                                    <input type="text" id="channelIdInput" class="form-input" placeholder="-1001234567890">
                                    <small class="form-help">Users must join this channel to use the bot (optional)</small>
                                </div>

                                <div class="form-group">
                                    <label for="adminNameInput">
                                        <i class="fas fa-user-shield"></i>
                                        Admin Panel Name
                                    </label>
                                    <input type="text" id="adminNameInput" class="form-input" placeholder="Bot Admin" value="Bot Admin">
                                    <small class="form-help">This name appears in the top-right corner of the admin panel</small>
                                </div>
                            </div>



                            <div class="form-actions">
                                <button id="saveBotConfigBtn" class="btn btn-primary">
                                    <i class="fas fa-save"></i>
                                    Save Configuration
                                </button>
                                <button id="testBotConfigBtn" class="btn btn-secondary">
                                    <i class="fas fa-vial"></i>
                                    Test Configuration
                                </button>
                                <button id="resetBotConfigBtn" class="btn btn-outline">
                                    <i class="fas fa-undo"></i>
                                    Reset to Defaults
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Bot Messages Management -->
                    <div class="dashboard-card messages-card">
                        <div class="card-header">
                            <div class="card-title">
                                <i class="fas fa-comments"></i>
                                <h3>Bot Messages Management</h3>
                            </div>
                            <div class="card-actions">
                                <button id="refreshMessagesBtn" class="btn btn-icon" title="Refresh Messages">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                                <button id="resetMessagesBtn" class="btn btn-icon" title="Reset to Defaults">
                                    <i class="fas fa-undo"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="messages-info">
                                <p>Customize all messages that the bot sends to users. Changes are applied immediately.</p>
                            </div>

                            <!-- Messages Search -->
                            <div class="messages-controls">
                                <div class="search-input-group">
                                    <input type="text" id="messageSearchInput" placeholder="Search messages..." class="search-input">
                                    <i class="fas fa-search search-icon"></i>
                                </div>
                            </div>

                            <!-- Category Filter Buttons -->
                            <div class="category-filter-buttons">
                                <button class="category-btn active" data-category="">
                                    <i class="fas fa-th-large"></i>
                                    <span>All Categories</span>
                                </button>
                                <button class="category-btn" data-category="welcome">
                                    <i class="fas fa-hand-wave"></i>
                                    <span>Welcome</span>
                                </button>
                                <button class="category-btn" data-category="file">
                                    <i class="fas fa-file-alt"></i>
                                    <span>File Processing</span>
                                </button>
                                <button class="category-btn" data-category="quiz">
                                    <i class="fas fa-question-circle"></i>
                                    <span>Quiz</span>
                                </button>
                                <button class="category-btn" data-category="error">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <span>Errors</span>
                                </button>
                                <button class="category-btn" data-category="success">
                                    <i class="fas fa-check-circle"></i>
                                    <span>Success</span>
                                </button>
                                <button class="category-btn" data-category="help">
                                    <i class="fas fa-question"></i>
                                    <span>Help</span>
                                </button>
                                <button class="category-btn" data-category="buttons">
                                    <i class="fas fa-mouse-pointer"></i>
                                    <span>Buttons</span>
                                </button>
                                <button class="category-btn" data-category="results">
                                    <i class="fas fa-trophy"></i>
                                    <span>Results</span>
                                </button>
                                <button class="category-btn" data-category="textinput">
                                    <i class="fas fa-keyboard"></i>
                                    <span>Text Input</span>
                                </button>
                                <button class="category-btn" data-category="mainMenu">
                                    <i class="fas fa-home"></i>
                                    <span>Main Menu</span>
                                </button>
                                <button class="category-btn" data-category="questionTypeSelection">
                                    <i class="fas fa-list-ul"></i>
                                    <span>Question Types</span>
                                </button>
                                <button class="category-btn" data-category="savedQuizzes">
                                    <i class="fas fa-save"></i>
                                    <span>Saved Quizzes</span>
                                </button>
                            </div>

                            <!-- Messages List -->
                            <div class="messages-container" id="messagesContainer">
                                <div class="loading-state" id="messagesLoading">
                                    <i class="fas fa-spinner fa-spin"></i>
                                    <span>Loading bot messages...</span>
                                </div>
                            </div>

                            <!-- Save All Changes Button -->
                            <div class="messages-actions">
                                <button id="reloadMessagesBtn" class="btn btn-secondary">
                                    <i class="fas fa-sync-alt"></i>
                                    Reload Messages
                                </button>
                                <button id="debugMessagesBtn" class="btn btn-info">
                                    <i class="fas fa-bug"></i>
                                    Debug
                                </button>
                                <button id="saveAllMessagesBtn" class="btn btn-primary">
                                    <i class="fas fa-save"></i>
                                    Save All Changes
                                </button>
                                <button id="resetMessagesBtn" class="btn btn-warning">
                                    <i class="fas fa-undo"></i>
                                    Reset to Arabic
                                </button>
                                <button id="restartBotBtn" class="btn btn-info">
                                    <i class="fas fa-power-off"></i>
                                    Restart Bot
                                </button>
                                <span class="save-status" id="messagesSaveStatus"></span>
                            </div>
                        </div>
                    </div>

                </div>
            </div>

            <!-- User Management Section -->
            <div id="user-statistics" class="admin-section">
                <div class="section-header">
                    <h1>User Management</h1>
                    <div class="header-controls-main">
                        <div class="search-input-group">
                            <input type="text" id="userSearchInput" placeholder="Search users..." class="search-input-small">
                            <select id="userTypeFilter" class="filter-select-small">
                                <option value="all">All</option>
                                <option value="free">Free</option>
                                <option value="paid">Paid</option>
                                <option value="expired">Expired</option>
                                <option value="banned">Banned</option>
                            </select>
                        </div>
                        <div class="users-actions">
                            <button id="refreshUsersBtn" class="btn btn-info btn-sm">
                                <i class="fas fa-sync-alt"></i>
                                Refresh
                            </button>
                            <button id="addPaidUserBtn" class="btn btn-primary btn-sm">
                                <i class="fas fa-user-plus"></i>
                                Add User
                            </button>
                            <button id="exportUsersBtn" class="btn btn-secondary btn-sm">
                                <i class="fas fa-download"></i>
                                Export
                            </button>
                        </div>
                    </div>
                </div>

                <div class="user-management-grid">
                    <!-- Users List -->
                    <div class="users-list-section">
                        <div class="users-card">
                            <div class="users-list" id="usersList">
                                <!-- Users will be loaded here dynamically -->
                                <div class="no-users-message" id="noUsersMessage">
                                    <i class="fas fa-users"></i>
                                    <h4>No Users Found</h4>
                                    <p>Users will appear here when they start using the bot</p>
                                </div>
                            </div>
                            <div class="users-pagination" id="usersPagination" style="display: none;">
                                <button id="prevUsersBtn" class="btn btn-secondary btn-sm">
                                    <i class="fas fa-chevron-left"></i>
                                    Previous
                                </button>
                                <span id="usersPageInfo">Page 1 of 1</span>
                                <button id="nextUsersBtn" class="btn btn-secondary btn-sm">
                                    Next
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                    </div>


                </div>
            </div>

            <!-- Rate Limits Section -->
            <div id="rate-limits" class="admin-section">
                <div class="section-header">
                    <h1>Rate Limits Configuration</h1>
                    <p>Configure file processing limits and cooldown periods for free and paid users</p>
                </div>

                <div class="rate-limits-container">
                    <!-- Free Users Configuration -->
                    <div class="rate-limit-card">
                        <div class="card-header">
                            <h3><i class="fas fa-user"></i> Free Users</h3>
                            <span class="user-type-badge free">FREE</span>
                        </div>
                        <div class="card-content">
                            <div class="limit-setting">
                                <label for="freeFilesPerDay">Files per Day</label>
                                <div class="input-group">
                                    <input type="number" id="freeFilesPerDay" min="1" max="100" value="5">
                                    <span class="input-suffix">files</span>
                                </div>
                                <small>Maximum number of files a free user can process per day</small>
                            </div>

                            <div class="limit-setting">
                                <label for="freeCooldownMinutes">Cooldown Period</label>
                                <div class="input-group">
                                    <input type="number" id="freeCooldownMinutes" min="0" max="60" value="5">
                                    <span class="input-suffix">minutes</span>
                                </div>
                                <small>Time users must wait between question generations</small>
                            </div>

                            <div class="limit-setting">
                                <label for="freeQuestionsPerPage">Questions per Page (Documents)</label>
                                <div class="input-group">
                                    <input type="number" id="freeQuestionsPerPage" min="1" max="10" value="5">
                                    <span class="input-suffix">per page</span>
                                </div>
                                <small>Questions per page for PDF, Word, and other documents</small>
                            </div>

                            <div class="limit-setting">
                                <label for="freeQuestionsPerImage">Questions per Image</label>
                                <div class="input-group">
                                    <input type="number" id="freeQuestionsPerImage" min="1" max="25" value="15">
                                    <span class="input-suffix">questions</span>
                                </div>
                                <small>Total questions generated from image files</small>
                            </div>
                        </div>
                    </div>

                    <!-- Paid Users Configuration -->
                    <div class="rate-limit-card">
                        <div class="card-header">
                            <h3><i class="fas fa-crown"></i> Paid Users</h3>
                            <span class="user-type-badge paid">PREMIUM</span>
                        </div>
                        <div class="card-content">
                            <div class="limit-setting">
                                <label for="paidFilesPerDay">Files per Day</label>
                                <div class="input-group">
                                    <input type="number" id="paidFilesPerDay" min="1" max="1000" value="100">
                                    <span class="input-suffix">files</span>
                                </div>
                                <small>Maximum number of files a premium user can process per day</small>
                            </div>

                            <div class="limit-setting">
                                <label for="paidCooldownMinutes">Cooldown Period</label>
                                <div class="input-group">
                                    <input type="number" id="paidCooldownMinutes" min="0" max="60" value="0">
                                    <span class="input-suffix">minutes</span>
                                </div>
                                <small>Time users must wait between question generations (0 = no cooldown)</small>
                            </div>

                            <div class="limit-setting">
                                <label for="paidQuestionsPerPage">Questions per Page (Documents)</label>
                                <div class="input-group">
                                    <input type="number" id="paidQuestionsPerPage" min="1" max="20" value="10">
                                    <span class="input-suffix">per page</span>
                                </div>
                                <small>Questions per page for PDF, Word, and other documents</small>
                            </div>

                            <div class="limit-setting">
                                <label for="paidQuestionsPerImage">Questions per Image</label>
                                <div class="input-group">
                                    <input type="number" id="paidQuestionsPerImage" min="1" max="50" value="25">
                                    <span class="input-suffix">questions</span>
                                </div>
                                <small>Total questions generated from image files</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Control Buttons -->
                <div class="rate-limits-controls">
                    <button id="saveRateLimitsBtn" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        Save Rate Limits
                    </button>
                    <button id="resetRateLimitsBtn" class="btn btn-secondary">
                        <i class="fas fa-undo"></i>
                        Reset to Defaults
                    </button>
                    <button id="viewUsageStatsBtn" class="btn btn-info">
                        <i class="fas fa-chart-line"></i>
                        View Usage Statistics
                    </button>
                </div>

                <!-- Usage Statistics -->
                <div class="usage-stats-container" id="usageStatsContainer" style="display: none;">
                    <h3>Current Usage Statistics</h3>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-content">
                                <h4 id="activeUsersCount">0</h4>
                                <p>Active Users Today</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-file-alt"></i>
                            </div>
                            <div class="stat-content">
                                <h4 id="totalFilesToday">0</h4>
                                <p>Files Processed Today</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stat-content">
                                <h4 id="usersInCooldown">0</h4>
                                <p>Users in Cooldown</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Subscription Duration Management -->
                <div class="rate-limits-card">
                    <div class="card-header">
                        <div class="header-icon">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div class="header-content">
                            <h3 class="card-title">Subscription Duration Management</h3>
                            <p class="card-subtitle">Manage user subscription durations and expiry times</p>
                        </div>
                    </div>

                    <div class="card-body">
                        <div class="duration-management-grid">
                            <!-- Add Duration to User -->
                            <div class="duration-section">
                                <h4><i class="fas fa-plus-circle"></i> Add Duration to User</h4>
                                <div class="duration-form">
                                    <div class="form-group">
                                        <label for="usernameInput">Username</label>
                                        <input type="text" id="usernameInput" placeholder="Enter username (without @)" class="form-control">
                                    </div>

                                    <div class="duration-inputs">
                                        <div class="duration-input-group">
                                            <label for="daysInput">Days</label>
                                            <input type="number" id="daysInput" min="0" max="365" value="0" class="form-control">
                                        </div>
                                        <div class="duration-input-group">
                                            <label for="hoursInput">Hours</label>
                                            <input type="number" id="hoursInput" min="0" max="23" value="0" class="form-control">
                                        </div>
                                        <div class="duration-input-group">
                                            <label for="minutesInput">Minutes</label>
                                            <input type="number" id="minutesInput" min="0" max="59" value="0" class="form-control">
                                        </div>
                                    </div>

                                    <div class="form-actions">
                                        <button id="addDurationBtn" class="btn btn-primary">
                                            <i class="fas fa-plus"></i>
                                            Add Duration
                                        </button>
                                        <button id="checkUserBtn" class="btn btn-secondary">
                                            <i class="fas fa-search"></i>
                                            Check User Status
                                        </button>
                                        <button id="createTestUserBtn" class="btn btn-info">
                                            <i class="fas fa-user-plus"></i>
                                            Create Test User
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Quick Duration Presets -->
                            <div class="duration-section">
                                <h4><i class="fas fa-clock"></i> Quick Duration Presets</h4>
                                <div class="preset-buttons">
                                    <button class="preset-btn" data-days="0" data-hours="1" data-minutes="0">
                                        <i class="fas fa-clock"></i>
                                        1 Hour
                                    </button>
                                    <button class="preset-btn" data-days="0" data-hours="6" data-minutes="0">
                                        <i class="fas fa-clock"></i>
                                        6 Hours
                                    </button>
                                    <button class="preset-btn" data-days="1" data-hours="0" data-minutes="0">
                                        <i class="fas fa-calendar-day"></i>
                                        1 Day
                                    </button>
                                    <button class="preset-btn" data-days="7" data-hours="0" data-minutes="0">
                                        <i class="fas fa-calendar-week"></i>
                                        1 Week
                                    </button>
                                    <button class="preset-btn" data-days="30" data-hours="0" data-minutes="0">
                                        <i class="fas fa-calendar-alt"></i>
                                        1 Month
                                    </button>
                                    <button class="preset-btn" data-days="365" data-hours="0" data-minutes="0">
                                        <i class="fas fa-calendar"></i>
                                        1 Year
                                    </button>
                                </div>
                            </div>

                            <!-- User Status Display -->
                            <div class="duration-section">
                                <h4><i class="fas fa-user-check"></i> User Status</h4>
                                <div id="userStatusDisplay" class="user-status-display">
                                    <p class="no-user-selected">Enter a username and click "Check User Status" to view subscription details</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

            <!-- Quiz Generator Screen (formerly Welcome Screen) -->
            <div id="quizGeneratorScreen" class="screen">
                <div class="quiz-generator-container">
                    <!-- Quiz Generator Navigation -->
                    <div class="quiz-generator-nav">
                        <button class="nav-btn active" id="generateTab" data-feature="generate">
                            <i class="fas fa-brain"></i>
                            <span data-translate="generateQuestions">Generate Questions</span>
                        </button>
                        <button class="nav-btn" id="historyTabNav" data-feature="history">
                            <i class="fas fa-history"></i>
                            <span data-translate="history">History</span>
                        </button>
                        <button class="nav-btn" id="statisticsTabNav" data-feature="statistics">
                            <i class="fas fa-chart-bar"></i>
                            <span data-translate="statistics">Statistics</span>
                        </button>
                    </div>

                    <!-- Main Content Area -->
                    <div class="quiz-generator-main-content">

                        <!-- Welcome Text -->
                        <div class="welcome-text">
                            <h2 data-translate="welcomeTitle">Welcome to Question Generator</h2>
                            <p data-translate="welcomeSubtitle">Generate multiple-choice and true/false questions from your educational content</p>
                        </div>

                        <div class="question-type-selection">
                            <h3 data-translate="chooseQuestionType">Choose Question Type</h3>
                            <div class="type-buttons">
                                <button id="mcqBtn" class="type-btn mcq-btn">
                                    <i class="fas fa-list-ul"></i>
                                    <span data-translate="multipleChoice">Multiple Choice (MCQ)</span>
                                    <small data-translate="multipleChoiceDesc">Generate questions with multiple options</small>
                                </button>
                                <button id="tfBtn" class="type-btn tf-btn">
                                    <i class="fas fa-check-circle"></i>
                                    <span data-translate="trueFalse">True/False (TF)</span>
                                    <small data-translate="trueFalseDesc">Generate true or false questions</small>
                                </button>
                            </div>
                        </div>

                        <div class="input-methods">
                            <h3 data-translate="addYourContent">Add Your Content</h3>
                            <div class="input-options">
                                <div class="input-option">
                                    <button id="textInputBtn" class="input-btn">
                                        <i class="fas fa-keyboard"></i>
                                        <span data-translate="typeText">Type Text</span>
                                    </button>
                                </div>
                                <div class="input-option">
                                    <button id="fileUploadBtn" class="input-btn">
                                        <i class="fas fa-file-upload"></i>
                                        <span data-translate="uploadFile">Upload File</span>
                                    </button>
                                </div>
                                <div class="input-option">
                                    <button id="imageUploadBtn" class="input-btn">
                                        <i class="fas fa-image"></i>
                                        <span data-translate="uploadImage">Upload Image</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

            <!-- PDF Editor Screen -->
            <div id="pdfEditorScreen" class="screen">
                <div class="pdf-editor-container">
                    <div class="screen-header">
                        <h2 data-translate="pdfEditor">PDF Editor</h2>
                        <p class="screen-subtitle" data-translate="pdfEditorSubtitle">Comprehensive PDF manipulation toolkit</p>
                    </div>

                    <!-- PDF Editor Navigation -->
                    <div class="pdf-editor-nav">
                        <button class="nav-btn active" id="imageToPdfTab" data-feature="imageToPdf">
                            <i class="fas fa-image"></i>
                            <span data-translate="imageToPdf">Image to PDF</span>
                        </button>
                        <button class="nav-btn" id="mergePdfTab" data-feature="mergePdf">
                            <i class="fas fa-layer-group"></i>
                            <span data-translate="mergePdf">Merge PDF</span>
                        </button>
                        <button class="nav-btn" id="splitPdfTab" data-feature="splitPdf">
                            <i class="fas fa-cut"></i>
                            <span data-translate="splitPdf">Split PDF</span>
                        </button>
                        <button class="nav-btn" id="textToPdfTab" data-feature="textToPdf">
                            <i class="fas fa-font"></i>
                            <span data-translate="textToPdf">Text to PDF</span>
                        </button>

                        <button class="nav-btn" id="deletePagesTab" data-feature="deletePages">
                            <i class="fas fa-trash-alt"></i>
                            <span data-translate="deletePages">Delete Pages</span>
                        </button>
                    </div>

                    <!-- PDF Editor Content Areas -->
                    <div class="pdf-editor-content">

                        <!-- Image to PDF Feature -->
                        <div id="imageToPdfContent" class="feature-content active">
                            <div class="feature-layout">
                                <div class="upload-section">
                                    <div class="upload-zone" id="pdfImageDropZone">
                                        <div class="upload-icon">
                                            <i class="fas fa-camera"></i>
                                        </div>
                                        <div class="upload-content">
                                            <h3 data-translate="dragDropImages">Drag and drop images here</h3>
                                            <p data-translate="orClickToSelect">or click to select files</p>
                                        </div>
                                        <div class="supported-formats">
                                            <span data-translate="supportedFormats">Supported formats:</span>
                                            <div class="format-tags">
                                                <span class="format-tag">JPG</span>
                                                <span class="format-tag">PNG</span>
                                                <span class="format-tag">BMP</span>
                                                <span class="format-tag">TIFF</span>
                                            </div>
                                        </div>
                                        <input type="file" id="pdfImageInput" accept=".jpg,.jpeg,.png,.bmp,.tiff,.tif" multiple hidden>
                                    </div>

                                    <!-- Selected Images Preview -->
                                    <div id="selectedImagesContainer" class="selected-images hidden">
                                        <div class="section-header">
                                            <h4 data-translate="selectedImages">Selected Images</h4>
                                            <button id="clearAllImages" class="btn btn-danger btn-sm">
                                                <i class="fas fa-trash"></i> <span data-translate="clearAll">Clear All</span>
                                            </button>
                                        </div>
                                        <div id="imagesList" class="images-list"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Merge PDF Feature -->
                        <div id="mergePdfContent" class="feature-content">
                            <div class="feature-layout">
                                <div class="upload-section">
                                    <div class="upload-zone" id="mergePdfDropZone">
                                        <div class="upload-icon">
                                            <i class="fas fa-layer-group"></i>
                                        </div>
                                        <div class="upload-content">
                                            <h3 data-translate="dragDropPdfs">Drag and drop PDF files here</h3>
                                            <p data-translate="orClickToSelectPdfs">or click to select PDF files</p>
                                        </div>
                                        <div class="supported-formats">
                                            <span data-translate="supportedFormats">Supported formats:</span>
                                            <div class="format-tags">
                                                <span class="format-tag">PDF</span>
                                            </div>
                                        </div>
                                        <input type="file" id="mergePdfInput" accept=".pdf" multiple hidden>
                                    </div>

                                    <!-- Selected PDFs Preview -->
                                    <div id="selectedPdfsContainer" class="selected-pdfs hidden">
                                        <div class="section-header">
                                            <h4 data-translate="selectedPdfs">Selected PDF Files</h4>
                                            <button id="clearAllPdfs" class="btn btn-danger btn-sm">
                                                <i class="fas fa-trash"></i> <span data-translate="clearAll">Clear All</span>
                                            </button>
                                        </div>
                                        <div id="pdfsList" class="pdfs-list"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Split PDF Feature -->
                        <div id="splitPdfContent" class="feature-content">
                            <div class="feature-layout">
                                <div class="upload-section">
                                    <div class="upload-zone" id="splitPdfDropZone">
                                        <div class="upload-icon">
                                            <i class="fas fa-cut"></i>
                                        </div>
                                        <div class="upload-content">
                                            <h3 data-translate="dragDropSinglePdf">Drag and drop a PDF file here</h3>
                                            <p data-translate="orClickToSelectSinglePdf">or click to select a PDF file</p>
                                        </div>
                                        <div class="supported-formats">
                                            <span data-translate="supportedFormats">Supported formats:</span>
                                            <div class="format-tags">
                                                <span class="format-tag">PDF</span>
                                            </div>
                                        </div>
                                        <input type="file" id="splitPdfInput" accept=".pdf" hidden>
                                    </div>

                                    <!-- Selected PDF Display -->
                                    <div id="selectedSplitPdfContainer" class="selected-pdfs hidden">
                                        <div class="section-header">
                                            <h4 data-translate="selectedPdf">Selected PDF</h4>
                                        </div>
                                        <div id="splitPdfDisplay" class="pdfs-list">
                                            <!-- PDF info will be displayed here -->
                                        </div>
                                    </div>

                                    <!-- Split Options -->
                                    <div id="splitOptionsContainer" class="split-options hidden">
                                        <div class="section-header">
                                            <h4 data-translate="splitOptions">Split Options</h4>
                                        </div>
                                        <div class="split-methods">
                                            <div class="method-option">
                                                <input type="radio" id="splitByPages" name="splitMethod" value="pages" checked>
                                                <label for="splitByPages" data-translate="splitByPages">Split by page ranges</label>
                                                <input type="text" id="pageRanges" placeholder="e.g., 1-5, 8-10, 15" class="page-input">
                                            </div>
                                            <div class="method-option">
                                                <input type="radio" id="splitEvery" name="splitMethod" value="every">
                                                <label for="splitEvery" data-translate="splitEvery">Split every N pages</label>
                                                <input type="number" id="everyNPages" min="1" value="1" class="page-input">
                                            </div>
                                            <div class="method-option">
                                                <input type="radio" id="splitIndividual" name="splitMethod" value="individual">
                                                <label for="splitIndividual" data-translate="splitIndividual">Split into individual pages</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Text to PDF Feature -->
                        <div id="textToPdfContent" class="feature-content">
                            <div class="feature-layout">
                                <div class="text-input-section">
                                    <div class="text-editor">
                                        <div class="editor-header">
                                            <h4 data-translate="textEditor">Text Editor</h4>
                                            <div class="editor-tools">
                                                <select id="fontFamily" class="font-select">
                                                    <option value="Arial">Arial</option>
                                                    <option value="Times New Roman">Times New Roman</option>
                                                    <option value="Helvetica">Helvetica</option>
                                                    <option value="Courier New">Courier New</option>
                                                </select>
                                                <input type="number" id="fontSize" min="8" max="72" value="12" class="font-size">
                                            </div>
                                        </div>
                                        <textarea id="textToPdfInput" placeholder="Enter your text here..." rows="15"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>



                        <!-- Delete Pages Feature -->
                        <div id="deletePagesContent" class="feature-content">
                            <div class="feature-layout">
                                <div class="upload-section">
                                    <div class="upload-zone" id="deletePagesDropZone">
                                        <div class="upload-icon">
                                            <i class="fas fa-trash-alt"></i>
                                        </div>
                                        <div class="upload-content">
                                            <h3 data-translate="dragDropPdfToEdit">Drag and drop a PDF file to edit</h3>
                                            <p data-translate="orClickToSelectPdfToEdit">or click to select a PDF file</p>
                                        </div>
                                        <div class="supported-formats">
                                            <span data-translate="supportedFormats">Supported formats:</span>
                                            <div class="format-tags">
                                                <span class="format-tag">PDF</span>
                                            </div>
                                        </div>
                                        <input type="file" id="deletePagesInput" accept=".pdf" hidden>
                                    </div>

                                    <!-- Selected PDF Display -->
                                    <div id="selectedDeletePdfContainer" class="selected-pdfs hidden">
                                        <div class="section-header">
                                            <h4 data-translate="selectedPdf">Selected PDF</h4>
                                        </div>
                                        <div id="deletePdfDisplay" class="pdfs-list">
                                            <!-- PDF info will be displayed here -->
                                        </div>
                                    </div>

                                    <!-- Page Selection -->
                                    <div id="pageSelectionContainer" class="page-selection hidden">
                                        <div class="section-header">
                                            <h4 data-translate="selectPagesToDelete">Select Pages to Delete</h4>
                                            <div class="selection-tools">
                                                <button id="selectAllPages" class="btn btn-sm">
                                                    <span data-translate="selectAll">Select All</span>
                                                </button>
                                                <button id="deselectAllPages" class="btn btn-sm">
                                                    <span data-translate="deselectAll">Deselect All</span>
                                                </button>
                                            </div>
                                        </div>
                                        <div id="pagesPreview" class="pages-preview"></div>
                                        <div class="page-range-input">
                                            <label for="pagesToDelete" data-translate="orEnterPageNumbers">Or enter page numbers:</label>
                                            <input type="text" id="pagesToDelete" placeholder="e.g., 1, 3-5, 8" class="page-input">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Settings Sidebar -->
                        <div class="pdf-settings-sidebar">
                            <!-- PDF Settings Card -->
                            <div class="settings-card" id="pdfSettingsCard">
                                <div class="card-header">
                                    <div class="header-icon">
                                        <i class="fas fa-cog"></i>
                                    </div>
                                    <div class="header-content">
                                        <h3 class="card-title" data-translate="pdfSettings">PDF Settings</h3>
                                        <p class="card-subtitle" data-translate="pdfSettingsDesc">Configure your PDF output</p>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <!-- Image to PDF Settings -->
                                    <div class="settings-group active" id="imageToPdfSettings">
                                        <div class="setting-group">
                                            <label for="pdfQuality" class="setting-label">
                                                <i class="fas fa-image"></i>
                                                <span data-translate="imageQuality">Image Quality</span>
                                            </label>
                                            <select id="pdfQuality" class="setting-select">
                                                <option value="high" data-translate="highQuality">High Quality</option>
                                                <option value="medium" data-translate="mediumQuality" selected>Medium Quality</option>
                                                <option value="low" data-translate="lowQuality">Low Quality</option>
                                            </select>
                                        </div>

                                        <div class="setting-group">
                                            <label for="pageSize" class="setting-label">
                                                <i class="fas fa-file-alt"></i>
                                                <span data-translate="pageSize">Page Size</span>
                                            </label>
                                            <select id="pageSize" class="setting-select">
                                                <option value="a4" data-translate="a4Size" selected>A4</option>
                                                <option value="letter" data-translate="letterSize">Letter</option>
                                                <option value="legal" data-translate="legalSize">Legal</option>
                                                <option value="auto" data-translate="autoSize">Auto (Fit Image)</option>
                                            </select>
                                        </div>



                                        <div class="setting-group">
                                            <label for="margin" class="setting-label">
                                                <i class="fas fa-expand-arrows-alt"></i>
                                                <span data-translate="margins">Margins</span>
                                            </label>
                                            <select id="margin" class="setting-select">
                                                <option value="none" data-translate="noMargins">No Margins</option>
                                                <option value="small" data-translate="smallMargins" selected>Small</option>
                                                <option value="medium" data-translate="mediumMargins">Medium</option>
                                                <option value="large" data-translate="largeMargins">Large</option>
                                            </select>
                                        </div>
                                    </div>

                                    <!-- Merge PDF Settings -->
                                    <div class="settings-group" id="mergePdfSettings">
                                        <div class="setting-group">
                                            <label for="mergeQuality" class="setting-label">
                                                <i class="fas fa-compress-alt"></i>
                                                <span data-translate="outputQuality">Output Quality</span>
                                            </label>
                                            <select id="mergeQuality" class="setting-select">
                                                <option value="high" data-translate="highQuality">High Quality (Larger file)</option>
                                                <option value="balanced" data-translate="balancedQuality" selected>Balanced</option>
                                                <option value="compressed" data-translate="compressedQuality">Compressed (Smaller file)</option>
                                            </select>
                                        </div>

                                        <div class="setting-group">
                                            <label class="checkbox-label">
                                                <input type="checkbox" id="maintainBookmarksSettings" checked>
                                                <span data-translate="maintainBookmarks">Maintain bookmarks</span>
                                            </label>
                                            <small class="setting-hint" data-translate="maintainBookmarksHint">Preserve navigation bookmarks from source PDFs</small>
                                        </div>

                                        <div class="setting-group">
                                            <label class="checkbox-label">
                                                <input type="checkbox" id="addTableOfContents">
                                                <span data-translate="addTableOfContents">Add Table of Contents</span>
                                            </label>
                                            <small class="setting-hint" data-translate="addTableOfContentsHint">Create a TOC page with source file names</small>
                                        </div>

                                        <div class="setting-group">
                                            <label class="checkbox-label">
                                                <input type="checkbox" id="addPageNumbers">
                                                <span data-translate="addPageNumbers">Add page numbers</span>
                                            </label>
                                            <small class="setting-hint" data-translate="addPageNumbersHint">Add page numbers to the merged PDF</small>
                                        </div>
                                    </div>

                                    <!-- Split PDF Settings -->
                                    <div class="settings-group" id="splitPdfSettings">
                                        <div class="setting-group">
                                            <label for="splitMethod" class="setting-label">
                                                <i class="fas fa-cut"></i>
                                                <span data-translate="splitMethod">Split Method</span>
                                            </label>
                                            <select id="splitMethod" class="setting-select">
                                                <option value="pages" data-translate="splitByPageRanges" selected>Split by page ranges</option>
                                                <option value="every" data-translate="splitEveryNPages">Split every N pages</option>
                                                <option value="extract" data-translate="extractSpecificPages">Extract specific pages</option>
                                            </select>
                                        </div>

                                        <div class="setting-group" id="pageRangesGroup">
                                            <label for="pageRangesInput" class="setting-label">
                                                <i class="fas fa-list-ol"></i>
                                                <span data-translate="pageRanges">Page Ranges</span>
                                            </label>
                                            <input type="text" id="pageRangesInput" class="setting-input" placeholder="e.g., 1-5, 8-10, 15">
                                            <small class="setting-hint" data-translate="pageRangesHint">Enter page ranges separated by commas (e.g., 1-5, 8-10, 15)</small>
                                        </div>

                                        <div class="setting-group hidden" id="everyNPagesGroup">
                                            <label for="everyNPagesInput" class="setting-label">
                                                <i class="fas fa-layer-group"></i>
                                                <span data-translate="pagesPerFile">Pages per file</span>
                                            </label>
                                            <input type="number" id="everyNPagesInput" class="setting-input" min="1" value="1">
                                            <small class="setting-hint" data-translate="pagesPerFileHint">Number of pages in each split file</small>
                                        </div>

                                        <div class="setting-group hidden" id="extractPagesGroup">
                                            <label for="extractPagesInput" class="setting-label">
                                                <i class="fas fa-file-export"></i>
                                                <span data-translate="extractPages">Pages to extract</span>
                                            </label>
                                            <input type="text" id="extractPagesInput" class="setting-input" placeholder="e.g., 1, 3, 5-7">
                                            <small class="setting-hint" data-translate="extractPagesHint">Enter specific pages to extract (e.g., 1, 3, 5-7)</small>
                                        </div>
                                    </div>

                                    <!-- Text to PDF Settings -->
                                    <div class="settings-group" id="textToPdfSettings">
                                        <div class="setting-group">
                                            <label for="textPageSize" class="setting-label">
                                                <i class="fas fa-file-alt"></i>
                                                <span data-translate="pageSize">Page Size</span>
                                            </label>
                                            <select id="textPageSize" class="setting-select">
                                                <option value="a4" data-translate="a4Size" selected>A4</option>
                                                <option value="letter" data-translate="letterSize">Letter</option>
                                                <option value="legal" data-translate="legalSize">Legal</option>
                                            </select>
                                        </div>

                                        <div class="setting-group">
                                            <label for="textMargins" class="setting-label">
                                                <i class="fas fa-expand-arrows-alt"></i>
                                                <span data-translate="margins">Margins</span>
                                            </label>
                                            <select id="textMargins" class="setting-select">
                                                <option value="small" data-translate="smallMargins">Small</option>
                                                <option value="medium" data-translate="mediumMargins" selected>Medium</option>
                                                <option value="large" data-translate="largeMargins">Large</option>
                                            </select>
                                        </div>

                                        <div class="setting-group">
                                            <label for="lineSpacing" class="setting-label">
                                                <i class="fas fa-align-justify"></i>
                                                <span data-translate="lineSpacing">Line Spacing</span>
                                            </label>
                                            <select id="lineSpacing" class="setting-select">
                                                <option value="1.0">1.0</option>
                                                <option value="1.15" selected>1.15</option>
                                                <option value="1.5">1.5</option>
                                                <option value="2.0">2.0</option>
                                            </select>
                                        </div>
                                    </div>

                                    <!-- Lock PDF Settings -->
                                    <div class="settings-group" id="lockPdfSettings">
                                        <div class="setting-group">
                                            <label for="lockUserPassword" class="setting-label">
                                                <i class="fas fa-key"></i>
                                                <span data-translate="userPassword">User Password</span>
                                            </label>
                                            <input type="password" id="lockUserPassword" class="setting-input" placeholder="Enter password to open PDF">
                                            <small class="setting-hint" data-translate="userPasswordHint">Password required to open and view the PDF</small>
                                        </div>

                                        <div class="setting-group">
                                            <label for="lockOwnerPassword" class="setting-label">
                                                <i class="fas fa-shield-alt"></i>
                                                <span data-translate="ownerPassword">Owner Password (Optional)</span>
                                            </label>
                                            <input type="password" id="lockOwnerPassword" class="setting-input" placeholder="Enter owner password">
                                            <small class="setting-hint" data-translate="ownerPasswordHint">Password for full permissions (editing, printing, etc.)</small>
                                        </div>

                                        <div class="setting-group">
                                            <label class="setting-label">
                                                <i class="fas fa-ban"></i>
                                                <span data-translate="permissions">Permissions</span>
                                            </label>
                                            <div class="checkbox-group">
                                                <label class="checkbox-label">
                                                    <input type="checkbox" id="allowPrinting" checked>
                                                    <span data-translate="allowPrinting">Allow printing</span>
                                                </label>
                                                <label class="checkbox-label">
                                                    <input type="checkbox" id="allowCopying" checked>
                                                    <span data-translate="allowCopying">Allow copying text</span>
                                                </label>
                                                <label class="checkbox-label">
                                                    <input type="checkbox" id="allowModifying">
                                                    <span data-translate="allowModifying">Allow modifying</span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Delete Pages Settings -->
                                    <div class="settings-group" id="deletePagesSettings">
                                        <div class="setting-group">
                                            <label for="deletePagesToDelete" class="setting-label">
                                                <i class="fas fa-trash-alt"></i>
                                                <span data-translate="pagesToDelete">Pages to Delete</span>
                                            </label>
                                            <input type="text" id="deletePagesToDelete" class="setting-input" placeholder="e.g., 1, 3, 5-7, 10">
                                            <small class="setting-hint" data-translate="pagesToDeleteHint">Enter page numbers to delete (e.g., 1, 3, 5-7, 10)</small>
                                        </div>

                                        <div class="setting-group">
                                            <label class="setting-label">
                                                <i class="fas fa-exclamation-triangle"></i>
                                                <span data-translate="deleteWarning">Warning</span>
                                            </label>
                                            <div class="warning-message">
                                                <small data-translate="deleteWarningMessage">Deleted pages cannot be recovered. Make sure to backup your original PDF.</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- PDF Actions -->
                            <div class="actions-card">
                                <div class="card-header">
                                    <div class="header-icon">
                                        <i class="fas fa-magic"></i>
                                    </div>
                                    <div class="header-content">
                                        <h3 class="card-title" id="actionCardTitle" data-translate="convertToPdf">Convert to PDF</h3>
                                        <p class="card-subtitle" id="actionCardSubtitle" data-translate="convertToPdfDesc">Generate your PDF document</p>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <!-- Image to PDF Action -->
                                    <div class="action-group hidden" id="imageToPdfAction">
                                        <button id="convertToPdfBtn" class="btn btn-primary btn-large" disabled>
                                            <i class="fas fa-file-pdf"></i>
                                            <span data-translate="generatePdf">Generate PDF</span>
                                        </button>
                                        <div class="conversion-info">
                                            <div class="info-item">
                                                <span data-translate="imagesSelected">Images Selected:</span>
                                                <span id="imageCount">0</span>
                                            </div>
                                            <div class="info-item">
                                                <span data-translate="estimatedSize">Estimated Size:</span>
                                                <span id="estimatedSize">0 MB</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Merge PDF Action -->
                                    <div class="action-group hidden" id="mergePdfAction">
                                        <button id="mergePdfsBtn" class="btn btn-primary btn-large" disabled>
                                            <i class="fas fa-layer-group"></i>
                                            <span data-translate="mergePdfs">Merge PDFs</span>
                                        </button>
                                        <div class="conversion-info">
                                            <div class="info-item">
                                                <span data-translate="pdfsSelected">PDFs Selected:</span>
                                                <span id="pdfCount">0</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Split PDF Action -->
                                    <div class="action-group hidden" id="splitPdfAction">
                                        <button id="splitPdfBtn" class="btn btn-primary btn-large" disabled>
                                            <i class="fas fa-cut"></i>
                                            <span data-translate="splitPdf">Split PDF</span>
                                        </button>
                                    </div>

                                    <!-- Text to PDF Action -->
                                    <div class="action-group hidden" id="textToPdfAction">
                                        <button id="textToPdfBtn" class="btn btn-primary btn-large">
                                            <i class="fas fa-font"></i>
                                            <span data-translate="createPdf">Create PDF</span>
                                        </button>
                                    </div>



                                    <!-- Delete Pages Action -->
                                    <div class="action-group hidden" id="deletePagesAction">
                                        <button id="deletePagesBtn" class="btn btn-primary btn-large" disabled>
                                            <i class="fas fa-trash-alt"></i>
                                            <span data-translate="deleteSelectedPages">Delete Selected Pages</span>
                                        </button>
                                        <div class="conversion-info">
                                            <div class="info-item">
                                                <span data-translate="pagesSelected">Pages Selected:</span>
                                                <span id="selectedPagesCount">0</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Mind Map Screen -->
            <div id="textToolsScreen" class="screen">
                <div class="text-tools-container">
                    <div class="screen-header">
                        <button id="backToHomeFromTextTools" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> <span data-translate="back">Back</span>
                        </button>
                        <h2 data-translate="mindMap">Mind Map</h2>
                        <p class="screen-subtitle" data-translate="mindMapSubtitle">AI-powered mind map generation from any content</p>
                    </div>

                    <!-- Mind Map Feature -->
                    <div id="mindMapContent" class="feature-content active">
                        <div class="mind-map-layout">
                            <!-- Input Section -->
                            <div class="mind-map-input-section">
                                <!-- Input methods moved to sidebar -->






                            </div>

                            <!-- Mind Map Display Section -->
                            <div class="mind-map-display-section">
                                <div class="mind-map-header">
                                    <h3 data-translate="mindMapVisualization">Mind Map Visualization</h3>
                                    <div class="mind-map-controls">
                                        <button id="zoomInBtn" class="btn btn-sm btn-secondary" title="Zoom In">
                                            <i class="fas fa-search-plus"></i>
                                        </button>
                                        <button id="zoomOutBtn" class="btn btn-sm btn-secondary" title="Zoom Out">
                                            <i class="fas fa-search-minus"></i>
                                        </button>
                                        <button id="resetZoomBtn" class="btn btn-sm btn-secondary" title="Reset Zoom">
                                            <i class="fas fa-expand-arrows-alt"></i>
                                        </button>
                                        <button id="saveMindMapBtn" class="btn btn-sm btn-success" title="Save Mind Map" disabled>
                                            <i class="fas fa-save"></i>
                                        </button>
                                        <button id="exportMindMapBtn" class="btn btn-sm btn-primary" title="Export Mind Map" disabled>
                                            <i class="fas fa-download"></i>
                                        </button>
                                    </div>
                                </div>
                                <div id="mindMapContainer" class="mind-map-container">
                                    <div class="mind-map-placeholder">
                                        <i class="fas fa-project-diagram"></i>
                                        <p data-translate="mindMapPlaceholder">Your mind map will appear here after generation</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Content Input Screen (Text Only) -->
            <div id="contentScreen" class="screen">
                <!-- Quiz Generator Navigation -->
                <div class="quiz-generator-nav">
                    <button class="nav-btn active" id="generateTabContent" data-feature="generate">
                        <i class="fas fa-brain"></i>
                        <span data-translate="generateQuestions">Generate Questions</span>
                    </button>
                    <button class="nav-btn" id="historyTabContent" data-feature="history">
                        <i class="fas fa-history"></i>
                        <span data-translate="history">History</span>
                    </button>
                    <button class="nav-btn" id="statisticsTabContent" data-feature="statistics">
                        <i class="fas fa-chart-bar"></i>
                        <span data-translate="statistics">Statistics</span>
                    </button>
                </div>
                <div class="content-container">
                    <div class="screen-header">
                        <button id="backToQuizGenerator" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> <span data-translate="back">Back</span>
                        </button>
                        <button id="backToHome" class="btn btn-secondary">
                            <i class="fas fa-home"></i> <span data-translate="home">Home</span>
                        </button>
                        <h2 id="contentScreenTitle" data-translate="addContent">Add Content</h2>
                    </div>

                    <!-- Text Input Only -->
                    <div id="textInputArea" class="input-area">
                        <textarea id="textContent" data-translate="enterContentPlaceholder" placeholder="Enter your educational content here..." rows="10"></textarea>
                        <div class="input-actions">
                            <button id="generateFromText" class="btn btn-primary">
                                <i class="fas fa-magic"></i> <span data-translate="generateQuestions">Generate Questions</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>



            <!-- Processing Screen -->
            <div id="processingScreen" class="screen">
                <div class="ai-processing-container">
                    <!-- Animated Background -->
                    <div class="processing-bg">
                        <div class="neural-network">
                            <div class="node node-1"></div>
                            <div class="node node-2"></div>
                            <div class="node node-3"></div>
                            <div class="node node-4"></div>
                            <div class="node node-5"></div>
                            <div class="connection conn-1"></div>
                            <div class="connection conn-2"></div>
                            <div class="connection conn-3"></div>
                            <div class="connection conn-4"></div>
                        </div>
                    </div>

                    <!-- Main Content -->
                    <div class="processing-content">
                        <!-- AI Brain Animation -->
                        <div class="ai-brain-container">
                            <div class="brain-core">
                                <div class="brain-pulse"></div>
                                <div class="brain-icon">
                                    <i class="fas fa-brain"></i>
                                </div>
                                <div class="thinking-dots">
                                    <span class="dot dot-1"></span>
                                    <span class="dot dot-2"></span>
                                    <span class="dot dot-3"></span>
                                </div>
                            </div>
                        </div>

                        <!-- Title and Status -->
                        <div class="processing-info">
                            <h1 class="processing-title">
                                <span class="title-gradient" data-translate="aiQuestionGenerator">AI Question Generator</span>
                            </h1>
                            <p id="processingStatus" class="processing-status" data-translate="initializingAiModels">Initializing AI models...</p>
                        </div>

                        <!-- Advanced Progress Bar -->
                        <div class="progress-container">
                            <div class="progress-track">
                                <div id="progressFill" class="progress-fill">
                                    <div class="progress-glow"></div>
                                </div>
                                <div class="progress-particles">
                                    <div class="particle particle-1"></div>
                                    <div class="particle particle-2"></div>
                                    <div class="particle particle-3"></div>
                                </div>
                            </div>
                            <div class="progress-info">
                                <span id="progressText" class="progress-percentage">0%</span>
                                <span id="progressETA" class="progress-eta" data-translate="calculating">Calculating...</span>
                            </div>
                        </div>

                        <!-- Processing Steps -->
                        <div class="processing-steps">
                            <div class="step step-1" id="step1">
                                <div class="step-icon"><i class="fas fa-file-text"></i></div>
                                <span class="step-text" data-translate="analyzingContent">Analyzing Content</span>
                            </div>
                            <div class="step step-2" id="step2">
                                <div class="step-icon"><i class="fas fa-cogs"></i></div>
                                <span class="step-text" data-translate="processingAiModels">Processing AI Models</span>
                            </div>
                            <div class="step step-3" id="step3">
                                <div class="step-icon"><i class="fas fa-lightbulb"></i></div>
                                <span class="step-text" data-translate="generatingQuestions">Generating Questions</span>
                            </div>
                            <div class="step step-4" id="step4">
                                <div class="step-icon"><i class="fas fa-check-circle"></i></div>
                                <span class="step-text" data-translate="finalizing">Finalizing</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Questions Display Screen -->
            <div id="questionsScreen" class="screen">
                <!-- Quiz Generator Navigation -->
                <div class="quiz-generator-nav">
                    <button class="nav-btn active" id="generateTabQuestions" data-feature="generate">
                        <i class="fas fa-brain"></i>
                        <span data-translate="generateQuestions">Generate Questions</span>
                    </button>
                    <button class="nav-btn" id="historyTabQuestions" data-feature="history">
                        <i class="fas fa-history"></i>
                        <span data-translate="history">History</span>
                    </button>
                    <button class="nav-btn" id="statisticsTabQuestions" data-feature="statistics">
                        <i class="fas fa-chart-bar"></i>
                        <span data-translate="statistics">Statistics</span>
                    </button>
                </div>
                <div class="questions-container">
                    <div class="screen-header">
                        <div class="header-nav">
                            <button id="backToContent" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> <span data-translate="back">Back</span>
                            </button>
                        </div>
                        <h2 data-translate="generatedQuestions">Generated Questions</h2>
                    </div>
                    
                    <div id="questionsDisplay" class="questions-display">
                        <!-- Questions will be dynamically inserted here -->
                    </div>
                </div>
            </div>

            <!-- Quiz Screen -->
            <div id="quizScreen" class="screen">
                <!-- Quiz Generator Navigation -->
                <div class="quiz-generator-nav">
                    <button class="nav-btn active" id="generateTabQuiz" data-feature="generate">
                        <i class="fas fa-brain"></i>
                        <span data-translate="generateQuestions">Generate Questions</span>
                    </button>
                    <button class="nav-btn" id="historyTabQuiz" data-feature="history">
                        <i class="fas fa-history"></i>
                        <span data-translate="history">History</span>
                    </button>
                    <button class="nav-btn" id="statisticsTabQuiz" data-feature="statistics">
                        <i class="fas fa-chart-bar"></i>
                        <span data-translate="statistics">Statistics</span>
                    </button>
                </div>
                <div class="quiz-container">
                    <div class="quiz-header">
                        <div class="quiz-nav">
                            <!-- Main menu button removed - using floating home button instead -->
                        </div>
                        <div class="quiz-progress">
                            <span id="questionNumber"><span data-translate="quiz">Quiz</span></span>
                            <span id="questionCount"><span data-translate="questions">Questions</span></span>
                        </div>
                        <div class="quiz-score">
                            <span data-translate="totalQuestions">Total: </span>
                            <span id="totalQuestions">0</span>
                        </div>
                    </div>

                    <div class="quiz-content">
                        <div id="allQuizQuestions" class="all-quiz-questions">
                            <!-- All questions will be displayed here -->
                        </div>

                        <div class="quiz-actions">
                            <button id="submitAllAnswers" class="btn btn-primary" disabled>
                                <span data-translate="submitQuiz">Submit Quiz</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Results Screen -->
            <div id="resultsScreen" class="screen">
                <!-- Quiz Generator Navigation -->
                <div class="quiz-generator-nav">
                    <button class="nav-btn active" id="generateTabResults" data-feature="generate">
                        <i class="fas fa-brain"></i>
                        <span data-translate="generateQuestions">Generate Questions</span>
                    </button>
                    <button class="nav-btn" id="historyTabResults" data-feature="history">
                        <i class="fas fa-history"></i>
                        <span data-translate="history">History</span>
                    </button>
                    <button class="nav-btn" id="statisticsTabResults" data-feature="statistics">
                        <i class="fas fa-chart-bar"></i>
                        <span data-translate="statistics">Statistics</span>
                    </button>
                </div>



                <div class="results-container">
                    <div class="results-header">
                        <i class="fas fa-trophy results-icon"></i>
                        <h2 data-translate="quizResults">Quiz Results</h2>
                    </div>
                    
                    <div id="resultsDisplay" class="results-display">
                        <!-- Results will be dynamically inserted here -->
                    </div>
                    
                    <!-- Detailed Review Section -->
                    <div id="detailedReview" class="detailed-review-section">
                        <!-- Review content will be dynamically inserted here -->
                    </div>



                </div>
            </div>
        </main>
        </div> <!-- End homeScreen -->

        <!-- Loading Overlay -->
        <div id="loadingOverlay" class="loading-overlay hidden">
            <div class="loading-spinner">
                <i class="fas fa-spinner fa-spin"></i>
                <p>Loading...</p>
            </div>
        </div>

        <!-- API Key Management Dialog -->
        <div id="apiKeyDialog" class="dialog-overlay" style="display: none;">
            <div class="dialog-content api-key-dialog">
                <div class="dialog-header">
                    <h3><i class="fas fa-key"></i> <span data-translate="apiKeyManager">API Key Manager</span></h3>
                    <button id="closeApiKeyDialog" class="close-btn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div class="dialog-body">
                    <div id="apiKeyAlertContainer"></div>

                    <!-- Current API Key Status -->
                    <div class="api-key-section">
                        <h4 data-translate="currentApiKey">Current API Key Status</h4>
                        <div id="currentApiKeyInfo" class="api-key-info">
                            <div class="loading-spinner">
                                <i class="fas fa-spinner fa-spin"></i>
                                <span data-translate="loading">Loading API key information...</span>
                            </div>
                        </div>
                    </div>

                    <!-- Update API Key -->
                    <div class="api-key-section">
                        <h4 data-translate="newApiKey">Update API Key</h4>
                        <div class="input-group">
                            <label for="newApiKeyInput" data-translate="enterNewApiKey">New OpenRouter API Key:</label>
                            <div class="input-with-toggle">
                                <input type="password" id="newApiKeyInput" placeholder="sk-or-v1-..." maxlength="73" class="api-key-input">
                                <button type="button" id="toggleApiKeyVisibility" class="toggle-btn">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="help-text">
                                Enter your new OpenRouter API key. It should start with "sk-or-v1-" and be 73 characters long.
                            </div>
                        </div>
                        <div class="button-group">
                            <button id="updateApiKeyBtn" class="btn btn-primary">
                                <i class="fas fa-save"></i> <span data-translate="updateApiKey">Update API Key</span>
                            </button>
                            <button id="testApiKeyBtn" class="btn btn-secondary">
                                <i class="fas fa-flask"></i> Test Current Key
                            </button>
                        </div>
                    </div>

                    <!-- Test Results -->
                    <div id="apiKeyTestResults" class="api-key-section" style="display: none;">
                        <h4 data-translate="testResults">Test Results</h4>
                        <div id="testResultsContent"></div>
                    </div>

                    <!-- Instructions -->
                    <div class="api-key-section">
                        <h4><i class="fas fa-info-circle"></i> <span data-translate="quickInstructions">Quick Instructions</span></h4>
                        <ol class="instructions-list">
                            <li>Visit <a href="https://openrouter.ai/" target="_blank">OpenRouter.ai</a> and create an account</li>
                            <li>Go to Settings → API Keys and create a new key</li>
                            <li>Go to Settings → Privacy and enable "Prompt Training"</li>
                            <li>Paste your new key above and click "Update API Key"</li>
                            <li>Use "Test Current Key" to verify it works</li>
                        </ol>
                    </div>
                </div>

                <div class="dialog-footer">
                    <button id="cancelApiKeyDialog" class="btn btn-secondary">
                        <i class="fas fa-times"></i> <span data-translate="close">Close</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- History Screen -->
        <div id="historyScreen" class="screen">
            <!-- Quiz Generator Navigation -->
            <div class="quiz-generator-nav">
                <button class="nav-btn" id="generateTabHistory" data-feature="generate">
                    <i class="fas fa-brain"></i>
                    <span data-translate="generateQuestions">Generate Questions</span>
                </button>
                <button class="nav-btn active" id="historyTabHistory" data-feature="history">
                    <i class="fas fa-history"></i>
                    <span data-translate="history">History</span>
                </button>
                <button class="nav-btn" id="statisticsTabHistory" data-feature="statistics">
                    <i class="fas fa-chart-bar"></i>
                    <span data-translate="statistics">Statistics</span>
                </button>
            </div>
            <div class="content-container">
                <div class="screen-header">
                    <h2><i class="fas fa-history"></i> <span data-translate="quizHistory">Quiz History</span></h2>
                    <div class="header-right">
                        <div id="historyRefreshIndicator" class="refresh-indicator" style="display: none;">
                            <i class="fas fa-check-circle text-success"></i> <span>Updated</span>
                        </div>
                        <button id="refreshHistoryBtn" class="btn btn-sm btn-primary" title="Refresh Now">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                        <button id="clearHistoryBtn" class="btn btn-danger">
                            <i class="fas fa-trash"></i> <span data-translate="clearHistory">Clear History</span>
                        </button>
                    </div>
                </div>

                <div class="history-content">


                    <div class="history-stats-summary">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-play-circle"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="totalQuizzesHistory">0</div>
                                <div class="stat-label" data-translate="totalQuizzes">Total Quizzes</div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-percentage"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="avgScoreHistory">0%</div>
                                <div class="stat-label" data-translate="averageScore">Average Score</div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="avgTimeHistory">0m</div>
                                <div class="stat-label" data-translate="averageTime">Average Time</div>
                            </div>
                        </div>
                    </div>



                    <!-- Quiz History Section -->
                    <div class="quiz-history-section">
                        <div class="section-header">
                            <h3><i class="fas fa-history"></i> <span data-translate="completedQuizzes">Completed Quizzes</span></h3>
                            <div class="section-actions">
                                <!-- Bulk selection controls removed since checkboxes were removed -->
                                <div class="history-filters-compact">
                                    <select id="historyDateFilter" class="filter-select-compact">
                                        <option value="all" data-translate="allTime">All Time</option>
                                        <option value="today" data-translate="today">Today</option>
                                        <option value="yesterday" data-translate="yesterday">Yesterday</option>
                                        <option value="week" data-translate="thisWeek">This Week</option>
                                        <option value="month" data-translate="thisMonth">This Month</option>
                                    </select>
                                    <select id="historyTypeFilter" class="filter-select-compact">
                                        <option value="all" data-translate="allTypes">All Types</option>
                                        <option value="MCQ" data-translate="multipleChoice">Multiple Choice</option>
                                        <option value="TF" data-translate="trueFalse">True/False</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="history-list" id="historyList">
                            <div class="loading-placeholder">
                                <i class="fas fa-spinner fa-spin"></i>
                                <p data-translate="loadingQuizHistory">Loading quiz history...</p>
                            </div>
                        </div>

                        <!-- History Pagination -->
                        <div class="pagination-container" id="historyPagination" style="display: none;">
                            <div class="pagination-info">
                                <span id="historyPageInfo">Page 1 of 1</span>
                            </div>
                            <div class="pagination-controls">
                                <button id="historyPrevBtn" class="btn btn-sm btn-secondary" disabled>
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                                <span id="historyPageNumbers" class="page-numbers"></span>
                                <button id="historyNextBtn" class="btn btn-sm btn-secondary" disabled>
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Saved Mind Maps Screen -->
        <div id="savedMindMapsScreen" class="screen">
            <div class="content-container">
                <div class="screen-header">
                    <div class="header-left">
                        <button id="backToMainFromSavedMaps" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> <span data-translate="back">Back</span>
                        </button>
                    </div>
                    <h2><i class="fas fa-bookmark"></i> <span data-translate="savedMindMaps">Saved Mind Maps</span></h2>
                    <div class="header-right">
                        <button id="clearAllMindMapsBtn" class="btn btn-sm btn-danger" title="Clear All Saved Maps">
                            <i class="fas fa-trash-alt"></i> <span data-translate="clearAll">Clear All</span>
                        </button>
                    </div>
                </div>

                <div class="saved-mindmaps-content">
                    <div id="savedMindMapsList" class="saved-maps-grid">
                        <!-- Saved mind maps will be populated here -->
                        <div class="no-saved-maps" id="noSavedMapsMessage">
                            <i class="fas fa-bookmark"></i>
                            <h3 data-translate="noSavedMaps">No Saved Mind Maps</h3>
                            <p data-translate="noSavedMapsDesc">Create and save mind maps to access them here</p>
                            <button id="createFirstMindMapBtn" class="btn btn-primary">
                                <i class="fas fa-plus"></i> <span data-translate="createMindMap">Create Mind Map</span>
                            </button>
                        </div>
                    </div>

                    <!-- Pagination Controls -->
                    <div id="savedMapsPagination" class="pagination-container" style="display: none;">
                        <div class="pagination-info">
                            <span id="paginationInfo">Showing 1-10 of 0 mind maps</span>
                        </div>
                        <div class="pagination-controls">
                            <button id="prevPageBtn" class="btn btn-secondary pagination-btn" disabled>
                                <i class="fas fa-chevron-left"></i> Previous
                            </button>
                            <div class="page-numbers" id="pageNumbers">
                                <!-- Page numbers will be generated here -->
                            </div>
                            <button id="nextPageBtn" class="btn btn-secondary pagination-btn" disabled>
                                Next <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Screen -->
        <div id="statisticsScreen" class="screen">
            <!-- Quiz Generator Navigation -->
            <div class="quiz-generator-nav">
                <button class="nav-btn" id="generateTabStats" data-feature="generate">
                    <i class="fas fa-brain"></i>
                    <span data-translate="generateQuestions">Generate Questions</span>
                </button>
                <button class="nav-btn" id="historyTabStats" data-feature="history">
                    <i class="fas fa-history"></i>
                    <span data-translate="history">History</span>
                </button>
                <button class="nav-btn active" id="statisticsTabStats" data-feature="statistics">
                    <i class="fas fa-chart-bar"></i>
                    <span data-translate="statistics">Statistics</span>
                </button>
            </div>
            <div class="content-container">
                <div class="screen-header">
                    <h2><i class="fas fa-chart-bar"></i> <span data-translate="statistics">Statistics</span></h2>
                    <div class="header-right">
                        <div id="statsRefreshIndicator" class="refresh-indicator" style="display: none;">
                            <i class="fas fa-check-circle text-success"></i> <span>Updated</span>
                        </div>
                        <button id="refreshStatsBtn" class="btn btn-sm btn-primary" title="Refresh Now">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                        <button id="exportStatsBtn" class="btn btn-primary">
                            <i class="fas fa-download"></i> <span data-translate="exportStats">Export Stats</span>
                        </button>
                    </div>
                </div>

                <div class="statistics-content">
                    <div class="stats-overview">
                        <div class="stat-card large">
                            <div class="stat-icon">
                                <i class="fas fa-trophy"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="overallScore">0%</div>
                                <div class="stat-label" data-translate="overallScore">Overall Score</div>
                                <div class="stat-sublabel" data-translate="acrossAllQuizzes">Across all quizzes</div>
                            </div>
                        </div>
                        <div class="stat-card large">
                            <div class="stat-icon">
                                <i class="fas fa-fire"></i>
                            </div>
                            <div class="stat-content">
                                <div class="stat-number" id="currentStreak">0</div>
                                <div class="stat-label" data-translate="currentStreak">Current Streak</div>
                                <div class="stat-sublabel" data-translate="daysInARow">Days in a row</div>
                            </div>
                        </div>
                    </div>

                    <div class="stats-grid">
                        <div class="stats-section">
                            <h3><i class="fas fa-chart-line"></i> <span data-translate="performanceMetrics">Performance Metrics</span></h3>
                            <div class="stats-cards">
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-question-circle"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-number" id="totalQuestions">0</div>
                                        <div class="stat-label" data-translate="totalQuestions">Total Questions</div>
                                    </div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-number" id="correctAnswers">0</div>
                                        <div class="stat-label" data-translate="correctAnswers">Correct Answers</div>
                                    </div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-times-circle"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-number" id="incorrectAnswers">0</div>
                                        <div class="stat-label" data-translate="incorrectAnswers">Incorrect Answers</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="stats-section">
                            <h3><i class="fas fa-chart-pie"></i> <span data-translate="questionTypes">Question Types</span></h3>
                            <div class="stats-cards">
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-list"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-number" id="mcqQuizzes">0</div>
                                        <div class="stat-label" data-translate="mcqQuizzes">MCQ Quizzes</div>
                                        <div class="stat-progress">
                                            <div class="progress-bar">
                                                <div class="progress-fill" id="mcqProgress"></div>
                                            </div>
                                            <span class="progress-text" id="mcqPercentage">0%</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-check"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-number" id="tfQuizzes">0</div>
                                        <div class="stat-label" data-translate="trueFalseQuizzes">True/False Quizzes</div>
                                        <div class="stat-progress">
                                            <div class="progress-bar">
                                                <div class="progress-fill" id="tfProgress"></div>
                                            </div>
                                            <span class="progress-text" id="tfPercentage">0%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="stats-section">
                            <h3><i class="fas fa-calendar-alt"></i> <span data-translate="activity">Activity</span></h3>
                            <div class="stats-cards">
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-calendar-day"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-number" id="quizzesToday">0</div>
                                        <div class="stat-label" data-translate="quizzesToday">Quizzes Today</div>
                                    </div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-calendar-week"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-number" id="quizzesThisWeek">0</div>
                                        <div class="stat-label" data-translate="thisWeek">This Week</div>
                                    </div>
                                </div>
                                <div class="stat-card">
                                    <div class="stat-icon">
                                        <i class="fas fa-calendar"></i>
                                    </div>
                                    <div class="stat-content">
                                        <div class="stat-number" id="quizzesThisMonth">0</div>
                                        <div class="stat-label" data-translate="thisMonth">This Month</div>
                                    </div>
                                </div>
                            </div>
                        </div>


                    </div>


                </div>
            </div>
        </div>

        <!-- Floating Back Button -->
        <button id="floatingBackBtn" class="floating-back-btn" title="Back to Main Menu" style="display: none;">
            <i class="fas fa-home"></i>
        </button>

        <!-- Notification Container -->
        <div id="notificationContainer" class="notification-container"></div>

        <!-- Model Management Dialogs -->

        <!-- Add Model Dialog -->
        <div id="addModelDialog" class="dialog-overlay" style="display: none;">
            <div class="dialog-content api-key-dialog">
                <div class="dialog-header">
                    <h3><i class="fas fa-plus"></i> <span data-translate="addNewAiModel">Add New AI Model</span></h3>
                    <button id="closeAddModelDialog" class="close-btn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="dialog-body">
                    <div class="form-group">
                        <label for="newModelId" data-translate="modelId">Model ID:</label>
                        <input type="text" id="newModelId" class="form-input" placeholder="e.g., openai/gpt-4:free" required>
                        <small class="form-help" data-translate="modelIdHelp">Enter the full model identifier (provider/model-name:tier)</small>
                    </div>
                    <div class="form-group">
                        <label for="newModelName" data-translate="displayName">Display Name:</label>
                        <input type="text" id="newModelName" class="form-input" placeholder="e.g., GPT-4 (Free)" required>
                        <small class="form-help" data-translate="displayNameHelp">Friendly name to display in the dropdown</small>
                    </div>
                    <div class="form-group">
                        <label for="newModelDescription" data-translate="descriptionOptional">Description (Optional):</label>
                        <input type="text" id="newModelDescription" class="form-input" placeholder="e.g., Advanced reasoning model">
                        <small class="form-help" data-translate="descriptionHelp">Brief description of the model's capabilities</small>
                    </div>
                </div>
                <div class="dialog-footer">
                    <button id="confirmAddModel" class="btn btn-primary">
                        <i class="fas fa-plus"></i> <span data-translate="addModel">Add Model</span>
                    </button>
                    <button id="cancelAddModel" class="btn btn-secondary">
                        <i class="fas fa-times"></i> <span data-translate="cancel">Cancel</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Remove Model Dialog -->
        <div id="removeModelDialog" class="dialog-overlay" style="display: none;">
            <div class="dialog-content api-key-dialog">
                <div class="dialog-header">
                    <h3><i class="fas fa-trash"></i> <span data-translate="removeAiModel">Remove AI Model</span></h3>
                    <button id="closeRemoveModelDialog" class="close-btn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="dialog-body">
                    <div class="form-group">
                        <label for="removeModelSelect" data-translate="selectModelToRemove">Select Model to Remove:</label>
                        <select id="removeModelSelect" class="form-select" required>
                            <option value="" data-translate="chooseModelToRemove">Choose a model to remove...</option>
                        </select>
                        <small class="form-help" data-translate="removeModelWarning">⚠️ Warning: This will permanently remove the model from your list. You can remove ANY model including all default models.</small>
                    </div>
                    <div id="removeModelInfo" class="model-info" style="display: none;">
                        <div class="info-item">
                            <strong data-translate="modelId">Model ID:</strong> <span id="removeModelId"></span>
                        </div>
                        <div class="info-item">
                            <strong data-translate="displayName">Display Name:</strong> <span id="removeModelName"></span>
                        </div>
                    </div>
                </div>
                <div class="dialog-footer">
                    <button id="confirmRemoveModel" class="btn btn-danger" disabled>
                        <i class="fas fa-trash"></i> <span data-translate="removeModel">Remove Model</span>
                    </button>
                    <button id="cancelRemoveModel" class="btn btn-secondary">
                        <i class="fas fa-times"></i> <span data-translate="cancel">Cancel</span>
                    </button>
                </div>
            </div>
        </div>



        <!-- Test Models Dialog -->
        <div id="testModelsDialog" class="dialog-overlay" style="display: none;">
            <div class="dialog-content api-key-dialog">
                <div class="dialog-header">
                    <h3><i class="fas fa-flask"></i> <span data-translate="modelTestingSimulation">Model Testing & Simulation</span></h3>
                    <button id="closeTestModelsDialog" class="close-btn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="dialog-body">
                    <div class="test-controls">
                        <div class="form-group">
                            <label for="testContent" data-translate="testContent">Test Content:</label>
                            <textarea id="testContent" class="form-input" rows="4" data-translate="testContentPlaceholder" placeholder="Enter test content for question generation...">The human heart has four chambers: two atria and two ventricles. Blood flows from the right atrium to the right ventricle, then to the lungs for oxygenation.</textarea>
                        </div>
                        <div class="form-group">
                            <label for="testQuestionType" data-translate="questionType">Question Type:</label>
                            <select id="testQuestionType" class="form-select">
                                <option value="MCQ" data-translate="multipleChoiceMcq">Multiple Choice (MCQ)</option>
                                <option value="TF" data-translate="trueFalseTf">True/False (TF)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="testQuestionCount" data-translate="questionCount">Question Count:</label>
                            <input type="number" id="testQuestionCount" class="form-input" value="3" min="1" max="10">
                        </div>
                        <div class="test-actions">
                            <button id="startModelTest" class="btn btn-primary">
                                <i class="fas fa-play"></i> <span data-translate="startTestingAllModels">Start Testing All Models</span>
                            </button>
                            <button id="stopModelTest" class="btn btn-danger" style="display: none;">
                                <i class="fas fa-stop"></i> <span data-translate="stopTesting">Stop Testing</span>
                            </button>
                            <button id="clearRateLimits" class="btn btn-warning">
                                <i class="fas fa-refresh"></i> <span data-translate="clearRateLimits">Clear Rate Limits</span>
                            </button>
                        </div>
                    </div>
                    <div class="test-results" id="testResults">
                        <div class="test-placeholder">
                            <i class="fas fa-flask"></i>
                            <p data-translate="clickStartTesting">Click "Start Testing" to test all available models</p>
                        </div>
                    </div>
                </div>
                <div class="dialog-footer">
                    <button id="exportTestResults" class="btn btn-primary" style="display: none;">
                        <i class="fas fa-download"></i> <span data-translate="exportResults">Export Results</span>
                    </button>
                    <button id="closeTestModels" class="btn btn-secondary">
                        <i class="fas fa-times"></i> <span data-translate="close">Close</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Floating Action Buttons for Results -->
    <div class="results-actions">
        <button id="takeAnotherQuizBtn" class="btn btn-primary">
            <i class="fas fa-plus"></i> <span data-translate="takeAnotherQuiz">Take Another Quiz</span>
        </button>
    </div>

    <!-- Floating Action Buttons for Generated Questions -->
    <div class="question-actions-floating">
        <button id="startQuizBtn" class="btn btn-primary">
            <i class="fas fa-play"></i> <span data-translate="startQuiz">Start Quiz</span>
        </button>
        <button id="showAnswersBtn" class="btn btn-secondary">
            <i class="fas fa-eye"></i> <span data-translate="showAnswers">Show Answers</span>
        </button>
        <button id="exportQuestionsBtn" class="btn btn-secondary">
            <i class="fas fa-download"></i> <span data-translate="exportPdf">Export PDF</span>
        </button>
    </div>

    <!-- Floating Action Buttons for Mind Map -->
    <div class="mindmap-actions-floating">
        <!-- Upload Content Section -->
        <div class="mindmap-upload-section">
            <h4 data-translate="addYourContent">Add Your Content</h4>
            <button id="mindMapFileUploadBtn" class="btn btn-primary mindmap-upload-btn">
                <i class="fas fa-file-upload"></i> <span data-translate="uploadFile">Upload File</span>
            </button>
            <button id="mindMapImageUploadBtn" class="btn btn-primary mindmap-upload-btn">
                <i class="fas fa-image"></i> <span data-translate="uploadImage">Upload Image</span>
            </button>
        </div>

        <!-- Saved Mind Maps Section -->
        <button id="viewSavedMindMapsBtn" class="btn btn-info">
            <i class="fas fa-bookmark"></i> <span data-translate="savedMindMaps">Saved Mind Maps</span>
        </button>
    </div>

    <!-- Save Mind Map Modal -->
    <div id="saveMindMapModal" class="modal-overlay" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-save"></i> <span data-translate="saveMindMapTitle">Save Mind Map</span></h3>
                <button class="modal-close" onclick="app.closeSaveMindMapModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <p data-translate="saveMindMapDesc">Enter a name for your mind map to save it for later access.</p>
                <div class="form-group">
                    <label for="mindMapNameInput" data-translate="mindMapNameLabel">Mind Map Name:</label>
                    <input type="text" id="mindMapNameInput" class="form-input" placeholder="My Mind Map" maxlength="100">
                    <small class="form-hint" data-translate="mindMapNameHint">Choose a descriptive name to easily find it later</small>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="app.closeSaveMindMapModal()">
                    <i class="fas fa-times"></i> <span data-translate="cancel">Cancel</span>
                </button>
                <button class="btn btn-success" onclick="app.confirmSaveMindMap()">
                    <i class="fas fa-save"></i> <span data-translate="saveMindMap">Save Mind Map</span>
                </button>
            </div>
        </div>
    </div>

    <!-- User Management Modal -->
    <div id="userManagementModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>User Management</h3>
                <button class="modal-close" id="closeUserModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="users-table-container">
                    <table class="users-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Username</th>
                                <th>Language</th>
                                <th>Joined</th>
                                <th>Last Activity</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="usersTableBody">
                            <!-- Users will be loaded here -->
                        </tbody>
                    </table>
                </div>
                <div class="pagination">
                    <button id="prevUsersPage" class="btn btn-secondary">Previous</button>
                    <span id="usersPageInfo">Page 1 of 1</span>
                    <button id="nextUsersPage" class="btn btn-secondary">Next</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add User Modal -->
    <div id="addUserModal" class="modal-overlay" style="display: none;">
        <div class="modal-content add-user-modal">
            <div class="modal-header">
                <h3>Add Paid User</h3>
                <button class="modal-close" onclick="closeAddUserModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="modalTelegramUsername">Telegram Username</label>
                    <input type="text" id="modalTelegramUsername" placeholder="@username or username" class="form-input">
                </div>
                <div class="form-group">
                    <label for="modalSubscriptionType">Subscription Type</label>
                    <select id="modalSubscriptionType" class="form-select">
                        <option value="monthly">Monthly (30 days)</option>
                        <option value="yearly">Yearly (365 days)</option>
                        <option value="custom">Custom Duration</option>
                    </select>
                </div>
                <div class="form-group" id="modalCustomDaysGroup" style="display: none;">
                    <label for="modalCustomDays">Custom Days</label>
                    <input type="number" id="modalCustomDays" placeholder="Enter number of days" class="form-input" min="1">
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeAddUserModal()">Cancel</button>
                <button id="modalAddPaidUserBtn" class="btn btn-primary">
                    <i class="fas fa-user-plus"></i>
                    Add User
                </button>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script src="translations.js"></script>
    <script src="app.js"></script>
    <script src="button-test.js"></script>
</body>
</html>
