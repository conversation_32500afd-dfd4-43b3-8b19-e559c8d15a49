const { contextBridge, ipc<PERSON>enderer } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // File operations
  selectFile: () => ipcRenderer.invoke('select-file'),
  processFile: (filePath) => ipcRenderer.invoke('process-file', filePath),
  saveFile: (options) => ipcRenderer.invoke('save-file', options),
  writeFile: (filePath, content) => ipcRenderer.invoke('write-file', filePath, content),
  
  // Question generation
  generateQuestions: (content, type, count, preferredModel) => ipcRenderer.invoke('generate-questions', content, type, count, preferredModel),

  // Gemini CLI Integration
  geminiCheckAuth: () => ipcRenderer.invoke('gemini-check-auth'),
  geminiStartAuth: () => ipcRenderer.invoke('gemini-start-auth'),
  geminiGenerateQuestions: (content, options) => ipcRenderer.invoke('gemini-generate-questions', content, options),
  geminiGenerateMindmap: (content, options) => ipcRenderer.invoke('gemini-generate-mindmap', content, options),
  geminiGetAuthStatus: () => ipcRenderer.invoke('gemini-get-auth-status'),
  geminiStopProcess: () => ipcRenderer.invoke('gemini-stop-process'),
  geminiPollAuth: () => ipcRenderer.invoke('gemini-poll-auth'),
  geminiSetupApiKey: (apiKey) => ipcRenderer.invoke('gemini-setup-apikey', apiKey),
  geminiStartTerminalAuth: () => ipcRenderer.invoke('gemini-start-terminal-auth'),
  geminiCheckAuthStatus: () => ipcRenderer.invoke('gemini-check-auth-status'),
  geminiOpenBrowserAuth: () => ipcRenderer.invoke('gemini-open-browser-auth'),
  geminiSignOut: () => ipcRenderer.invoke('gemini-sign-out'),
  openTerminalAuth: () => ipcRenderer.invoke('open-terminal-auth'),
  closeBrowserWindows: () => ipcRenderer.invoke('close-browser-windows'),
  closeTerminalWindows: () => ipcRenderer.invoke('close-terminal-windows'),
  onGeminiAuthSuccess: (callback) => ipcRenderer.on('gemini-auth-success', callback),
  onUserDataUpdated: (callback) => ipcRenderer.on('user-data-updated', callback),

  // Account Management
  accountGetSavedAccounts: () => ipcRenderer.invoke('account-get-saved-accounts'),
  accountSwitchTo: (email) => ipcRenderer.invoke('account-switch-to', email),
  accountAutoSaveCurrent: () => ipcRenderer.invoke('account-auto-save-current'),
  accountRemove: (email) => ipcRenderer.invoke('account-remove', email),
  accountGetCurrent: () => ipcRenderer.invoke('account-get-current'),
  accountSwitchOnRateLimit: () => ipcRenderer.invoke('account-switch-on-rate-limit'),

  // Telegram Bot Management
  telegramBotStart: () => ipcRenderer.invoke('telegram-bot-start'),
  telegramBotStop: () => ipcRenderer.invoke('telegram-bot-stop'),
  telegramBotStatus: () => ipcRenderer.invoke('telegram-bot-status'),
  telegramBotForceStop: () => ipcRenderer.invoke('telegram-bot-force-stop'),
  telegramBotConfig: (config) => ipcRenderer.invoke('telegram-bot-config', config),
  testBotConfiguration: (config) => ipcRenderer.invoke('test-bot-configuration', config),
  getTelegramUsers: (page, limit) => ipcRenderer.invoke('get-telegram-users', page, limit),
  banTelegramUser: (userId) => ipcRenderer.invoke('ban-telegram-user', userId),
  unbanTelegramUser: (userId) => ipcRenderer.invoke('unban-telegram-user', userId),
  banUser: (userId) => ipcRenderer.invoke('ban-telegram-user', userId),
  addTelegramUser: (userData) => ipcRenderer.invoke('add-telegram-user', userData),
  unbanUser: (userId) => ipcRenderer.invoke('unban-telegram-user', userId),

  // Bot Messages Management
  getBotMessages: () => ipcRenderer.invoke('get-bot-messages'),
  updateBotMessage: (messageKey, newMessage) => ipcRenderer.invoke('update-bot-message', messageKey, newMessage),
  updateBotMessages: (updates) => ipcRenderer.invoke('update-bot-messages', updates),
  resetBotMessages: () => ipcRenderer.invoke('reset-bot-messages'),
  reloadBotMessages: () => ipcRenderer.invoke('reload-bot-messages'),

  // Rate Limits Management
  getRateLimits: () => ipcRenderer.invoke('get-rate-limits'),
  updateRateLimits: (limits) => ipcRenderer.invoke('update-rate-limits', limits),
  getUsageStats: () => ipcRenderer.invoke('get-usage-stats'),
  createTestUsers: () => ipcRenderer.invoke('create-test-users'),

  // Duration Management
  addUserDuration: (userId, totalMinutes) => ipcRenderer.invoke('add-user-duration', userId, totalMinutes),
  getUserStatus: (userId) => ipcRenderer.invoke('get-user-status', userId),
  addUserDurationByUsername: (username, totalMinutes) => ipcRenderer.invoke('add-user-duration-by-username', username, totalMinutes),
  getUserStatusByUsername: (username) => ipcRenderer.invoke('get-user-status-by-username', username),


  // Mind map generation
  generateMindMap: (content, isScanned) => ipcRenderer.invoke('generate-mind-map', content, isScanned),
  
  // Quiz operations
  startQuiz: (questions) => ipcRenderer.invoke('start-quiz', questions),
  submitAnswer: (questionIndex, answer) => ipcRenderer.invoke('submit-answer', questionIndex, answer),
  getQuizResults: () => ipcRenderer.invoke('get-quiz-results'),
  
  // Database operations
  saveQuizSession: (session) => ipcRenderer.invoke('save-quiz-session', session),
  getQuizHistory: () => ipcRenderer.invoke('get-quiz-history'),
  getStatistics: () => ipcRenderer.invoke('get-statistics'),
  getOverallStats: () => ipcRenderer.invoke('get-overall-stats'),
  clearQuizHistory: () => ipcRenderer.invoke('clear-quiz-history'),
  clearSingleQuizHistory: (sessionId) => ipcRenderer.invoke('clear-single-quiz-history', sessionId),


  
  // API Key Management
  getApiKeyInfo: () => ipcRenderer.invoke('get-api-key-info'),
  updateApiKey: (newApiKey) => ipcRenderer.invoke('update-api-key', newApiKey),
  testApiKey: (testKey) => ipcRenderer.invoke('test-api-key', testKey),

  // Settings
  getSettings: () => ipcRenderer.invoke('get-settings'),
  saveSettings: (settings) => ipcRenderer.invoke('save-settings', settings),
  
  // Feedback
  submitFeedback: (feedback) => ipcRenderer.invoke('submit-feedback', feedback),
  
  // Menu events
  onMenuAction: (callback) => ipcRenderer.on('menu-action', callback),
  onFileSelected: (callback) => ipcRenderer.on('file-selected', callback),
  
  // Menu specific events
  onNewQuiz: (callback) => ipcRenderer.on('menu-new-quiz', callback),
  onGenerateMCQ: (callback) => ipcRenderer.on('menu-generate-mcq', callback),
  onGenerateTF: (callback) => ipcRenderer.on('menu-generate-tf', callback),
  onStartQuiz: (callback) => ipcRenderer.on('menu-start-quiz', callback),

  onStatistics: (callback) => ipcRenderer.on('menu-statistics', callback),
  onHistory: (callback) => ipcRenderer.on('menu-history', callback),
  onAbout: (callback) => ipcRenderer.on('menu-about', callback),
  onHelp: (callback) => ipcRenderer.on('menu-help', callback),
  
  // Progress updates
  onProgressUpdate: (callback) => ipcRenderer.on('progress-update', callback),
  
  // Error handling
  onError: (callback) => ipcRenderer.on('error', callback),
  
  // Export functions
  saveFile: (options) => ipcRenderer.invoke('save-file', options),
  savePDF: (filePath, htmlContent) => ipcRenderer.invoke('save-pdf', filePath, htmlContent),
  saveImageAsPDF: (filePath, imageData) => ipcRenderer.invoke('save-image-as-pdf', filePath, imageData),
  saveFileContent: (filePath, content) => ipcRenderer.invoke('save-file-content', filePath, content),

  // Dialog functions
  showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
  showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),
  showMessageBox: (options) => ipcRenderer.invoke('show-message-box', options),

  // Model Management
  addModel: (modelData) => ipcRenderer.invoke('add-model', modelData),
  removeModel: (modelId) => ipcRenderer.invoke('remove-model', modelId),
  getAllModels: () => ipcRenderer.invoke('get-all-models'),
  checkModelAvailability: (modelId) => ipcRenderer.invoke('check-model-availability', modelId),
  testModel: (modelId, content, questionType, questionCount) => ipcRenderer.invoke('test-model', modelId, content, questionType, questionCount),
  runModelSimulation: (testParams) => ipcRenderer.invoke('run-model-simulation', testParams),
  testTrueFalseLogic: (testQuestions) => ipcRenderer.invoke('test-tf-logic', testQuestions),

  // Rate Limit Management
  getRateLimitedModels: () => ipcRenderer.invoke('get-rate-limited-models'),
  clearModelRateLimit: (modelId) => ipcRenderer.invoke('clear-model-rate-limit', modelId),
  clearAllRateLimits: () => ipcRenderer.invoke('clear-all-rate-limits'),

  // PDF Operations
  mergePdfs: (outputPath, pdfBuffers, options) => ipcRenderer.invoke('merge-pdfs', outputPath, pdfBuffers, options),
  splitPdf: (pdfBuffer, outputDir, splitOptions) => ipcRenderer.invoke('split-pdf', pdfBuffer, outputDir, splitOptions),
  lockPdf: (pdfBuffer, outputPath, passwordOptions) => ipcRenderer.invoke('lock-pdf', pdfBuffer, outputPath, passwordOptions),
  deletePdfPages: (pdfBuffer, outputPath, pagesToDelete) => ipcRenderer.invoke('delete-pdf-pages', pdfBuffer, outputPath, pagesToDelete),
  createTextPdf: (outputPath, textContent, options) => ipcRenderer.invoke('create-text-pdf', outputPath, textContent, options),
  validatePdf: (pdfBuffer) => ipcRenderer.invoke('validate-pdf', pdfBuffer),
  getPdfPageCount: (pdfBuffer) => ipcRenderer.invoke('get-pdf-page-count', pdfBuffer),

  // Utility functions
  showNotification: (title, body) => ipcRenderer.invoke('show-notification', title, body),
  openExternal: (url) => ipcRenderer.invoke('open-external', url),
  showMessageBox: (options) => ipcRenderer.invoke('show-message-box', options),
  
  // Remove listeners
  removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel),

  // User Management
  getUserStats: () => ipcRenderer.invoke('get-user-stats'),
  getUsersWithStats: (page, limit, filter, search) => ipcRenderer.invoke('get-users-with-stats', page, limit, filter, search),
  getSubscriptionStats: () => ipcRenderer.invoke('get-subscription-stats'),
  invoke: (channel, ...args) => ipcRenderer.invoke(channel, ...args)
});

// Expose some Node.js APIs that are safe to use in the renderer
contextBridge.exposeInMainWorld('nodeAPI', {
  platform: process.platform,
  versions: process.versions,

  // Real-time updates
  onUserDataUpdated: (callback) => ipcRenderer.on('user-data-updated', callback)
});

// Expose application info
contextBridge.exposeInMainWorld('appInfo', {
  name: 'MCQ & TF Question Generator',
  version: '1.0.0',
  description: 'Desktop application for generating multiple-choice and true/false questions'
});
