const logger = require('../utils/logger');

class RateLimitService {
    constructor() {
        this.userLimits = new Map(); // Store user rate limit data
        this.defaultLimits = {
            free: {
                filesPerDay: 5,
                cooldownMinutes: 5,
                questionsPerFile: 20
            },
            paid: {
                filesPerDay: 100,
                cooldownMinutes: 0,
                questionsPerFile: 50
            }
        };
    }

    /**
     * Get rate limits for a user type
     */
    getLimits(userType = 'free') {
        return this.defaultLimits[userType] || this.defaultLimits.free;
    }

    /**
     * Update rate limits for a user type
     */
    updateLimits(userType, limits) {
        if (!this.defaultLimits[userType]) {
            this.defaultLimits[userType] = {};
        }
        Object.assign(this.defaultLimits[userType], limits);
        logger.info(`Updated rate limits for ${userType}:`, limits);
    }

    /**
     * Get user's current usage data
     */
    getUserUsage(userId) {
        const today = new Date().toDateString();
        
        if (!this.userLimits.has(userId)) {
            this.userLimits.set(userId, {
                lastRequestDate: today,
                filesProcessedToday: 0,
                lastQuestionGeneration: null
            });
        }

        const userData = this.userLimits.get(userId);
        
        // Reset daily counter if it's a new day
        if (userData.lastRequestDate !== today) {
            userData.lastRequestDate = today;
            userData.filesProcessedToday = 0;
        }

        return userData;
    }

    /**
     * Check if user can process a file (daily limit check)
     */
    canProcessFile(userId, userType = 'free') {
        const limits = this.getLimits(userType);
        const usage = this.getUserUsage(userId);

        const canProcess = usage.filesProcessedToday < limits.filesPerDay;
        
        logger.info(`File limit check for user ${userId} (${userType}): ${usage.filesProcessedToday}/${limits.filesPerDay} - ${canProcess ? 'ALLOWED' : 'BLOCKED'}`);
        
        return {
            allowed: canProcess,
            current: usage.filesProcessedToday,
            limit: limits.filesPerDay,
            remaining: Math.max(0, limits.filesPerDay - usage.filesProcessedToday)
        };
    }

    /**
     * Check if user can generate questions (cooldown check)
     */
    canGenerateQuestions(userId, userType = 'free') {
        const limits = this.getLimits(userType);
        const usage = this.getUserUsage(userId);

        if (limits.cooldownMinutes === 0) {
            return {
                allowed: true,
                remainingCooldown: 0
            };
        }

        if (!usage.lastQuestionGeneration) {
            return {
                allowed: true,
                remainingCooldown: 0
            };
        }

        const now = new Date();
        const lastGeneration = new Date(usage.lastQuestionGeneration);
        const timeDiff = now - lastGeneration;
        const cooldownMs = limits.cooldownMinutes * 60 * 1000;
        const remainingMs = cooldownMs - timeDiff;

        const canGenerate = timeDiff >= cooldownMs;
        const remainingMinutes = Math.ceil(remainingMs / (60 * 1000));

        logger.info(`Cooldown check for user ${userId} (${userType}): ${Math.floor(timeDiff / 60000)}min since last - ${canGenerate ? 'ALLOWED' : 'BLOCKED'}`);

        return {
            allowed: canGenerate,
            remainingCooldown: canGenerate ? 0 : remainingMinutes
        };
    }

    /**
     * Record file processing for a user
     */
    recordFileProcessing(userId) {
        const usage = this.getUserUsage(userId);
        usage.filesProcessedToday++;
        
        logger.info(`Recorded file processing for user ${userId}: ${usage.filesProcessedToday} files today`);
    }

    /**
     * Record question generation for a user
     */
    recordQuestionGeneration(userId) {
        const usage = this.getUserUsage(userId);
        usage.lastQuestionGeneration = new Date();
        
        logger.info(`Recorded question generation for user ${userId} at ${usage.lastQuestionGeneration}`);
    }

    /**
     * Get comprehensive rate limit status for a user
     */
    getUserStatus(userId, userType = 'free') {
        const limits = this.getLimits(userType);
        const fileCheck = this.canProcessFile(userId, userType);
        const cooldownCheck = this.canGenerateQuestions(userId, userType);

        return {
            userType,
            limits,
            files: fileCheck,
            cooldown: cooldownCheck,
            canProceed: fileCheck.allowed && cooldownCheck.allowed
        };
    }

    /**
     * Reset user's daily limits (for testing or admin purposes)
     */
    resetUserLimits(userId) {
        if (this.userLimits.has(userId)) {
            this.userLimits.delete(userId);
            logger.info(`Reset rate limits for user ${userId}`);
        }
    }

    /**
     * Get all current rate limits configuration
     */
    getAllLimits() {
        return {
            free: { ...this.defaultLimits.free },
            paid: { ...this.defaultLimits.paid }
        };
    }

    /**
     * Get usage statistics for all users
     */
    getUsageStats() {
        const stats = {
            totalActiveUsers: this.userLimits.size,
            usersWithActivity: 0,
            totalFilesToday: 0
        };

        const today = new Date().toDateString();
        
        for (const [userId, usage] of this.userLimits.entries()) {
            if (usage.lastRequestDate === today && usage.filesProcessedToday > 0) {
                stats.usersWithActivity++;
                stats.totalFilesToday += usage.filesProcessedToday;
            }
        }

        return stats;
    }
}

// Create singleton instance
const rateLimitService = new RateLimitService();

module.exports = rateLimitService;
