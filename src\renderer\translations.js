// Language translations for the MCQ & TF Generator
const translations = {
    en: {
        // Header
        appTitle: "MCQ & TF Generator",
        switchLanguage: "Switch Language",
        history: "History",
        statistics: "Statistics",
        backToMain: "Back to Main",
        
        // Home Screen
        appMainTitle: "X-Stein",
        appMainSubtitle: "Your ALL-IN ONE STUDY ENGINE POWERED BY AI",
        desktopUser: "Desktop User",
        notSignedIn: "Not Signed In",

        // Services
        quizGenerator: "Quiz Generator",
        quizGeneratorDesc: "Generate MCQ & True/False questions from any content",
        pdfEditor: "PDF Editor",
        pdfEditorDesc: "Comprehensive PDF manipulation toolkit with advanced features",
        pdfEditorSubtitle: "Comprehensive PDF manipulation toolkit",
        imageToPdf: "Image to PDF",
        imageToPdfDesc: "Convert images to PDF documents with advanced options",
        mergePdf: "Merge PDF",
        splitPdf: "Split PDF",

        textToPdf: "Text to PDF",
        deletePages: "Delete Pages",

        textTools: "Text Tools",
        textToolsDesc: "Advanced text processing and analysis tools",
        textToolsSubtitle: "AI-powered text analysis and visualization",
        mindMapDesc: "AI-powered mind map generation from any content",
        mindMapSubtitle: "AI-powered mind map generation from any content",
        visualization: "Visualization",
        studyTools: "Study Tools",
        studyToolsDesc: "Flashcards, spaced repetition, and study planning",
        aiAssistant: "AI Assistant",
        aiAssistantDesc: "Chat with AI for help, explanations, and more",
        savedMindMaps: "Saved Mind Maps",
        savedMindMapsDesc: "Access and manage your saved mind maps",

        // Service Features
        aiPowered: "AI Powered",
        multipleFormats: "Multiple Formats",
        interactive: "Interactive",
        batchConvert: "Batch Convert",
        highQuality: "High Quality",
        comingSoon: "Coming Soon",
        multiFormat: "Multi-Format",
        batchProcess: "Batch Process",
        ocr: "OCR",
        organized: "Organized",
        accessible: "Accessible",
        manageable: "Manageable",
        analysis: "Analysis",
        mindMap: "Mind Map",
        spacedRepetition: "Spaced Repetition",
        analytics: "Analytics",
        multiModel: "Multi-Model",
        contextAware: "Context Aware",

        // Actions
        launch: "Launch",
        soon: "Soon",
        home: "Home",

        // Stats
        questionsGenerated: "Questions Generated",
        filesProcessed: "Files Processed",
        mindMapsGenerated: "Mind Maps Generated",

        // Image to PDF Converter
        dragDropImages: "Drag and drop images here",
        orClickToSelect: "or click to select files",
        supportedFormats: "Supported formats:",
        selectedImages: "Selected Images",
        clearAll: "Clear All",
        pdfSettings: "PDF Settings",
        pdfSettingsDesc: "Configure your PDF output",
        imageQuality: "Image Quality",
        highQuality: "High Quality",
        mediumQuality: "Medium Quality",
        lowQuality: "Low Quality",
        pageSize: "Page Size",
        a4Size: "A4",
        letterSize: "Letter",
        legalSize: "Legal",
        autoSize: "Auto (Fit Image)",

        margins: "Margins",
        noMargins: "No Margins",
        smallMargins: "Small",
        mediumMargins: "Medium",
        largeMargins: "Large",
        convertToPdf: "Convert to PDF",
        convertToPdfDesc: "Generate your PDF document",
        generatePdf: "Generate PDF",
        imagesSelected: "Images Selected:",
        estimatedSize: "Estimated Size:",

        // PDF Editor Features
        dragDropPdfs: "Drag and drop PDF files here",
        orClickToSelectPdfs: "or click to select PDF files",
        selectedPdfs: "Selected PDF Files",
        pdfsSelected: "PDFs Selected:",
        mergePdfs: "Merge PDFs",
        maintainBookmarks: "Maintain bookmarks",
        maintainBookmarksHint: "Preserve navigation bookmarks from source PDFs",
        outputQuality: "Output Quality",
        balancedQuality: "Balanced",
        compressedQuality: "Compressed (Smaller file)",
        addTableOfContents: "Add Table of Contents",
        addTableOfContentsHint: "Create a TOC page with source file names",
        addPageNumbers: "Add page numbers",
        addPageNumbersHint: "Add page numbers to the merged PDF",

        // Split PDF
        splitMethod: "Split Method",
        splitByPageRanges: "Split by page ranges",
        splitEveryNPages: "Split every N pages",
        extractSpecificPages: "Extract specific pages",
        pageRanges: "Page Ranges",
        pageRangesHint: "Enter page ranges separated by commas (e.g., 1-5, 8-10, 15)",
        pagesPerFile: "Pages per file",
        pagesPerFileHint: "Number of pages in each split file",
        extractPages: "Pages to extract",
        extractPagesHint: "Enter specific pages to extract (e.g., 1, 3, 5-7)",



        // Delete Pages
        pagesToDelete: "Pages to Delete",
        pagesToDeleteHint: "Enter page numbers to delete (e.g., 1, 3, 5-7, 10)",
        deleteWarning: "Warning",
        deleteWarningMessage: "Deleted pages cannot be recovered. Make sure to backup your original PDF.",

        // Text to PDF
        textEditor: "Text Editor",
        margins: "Margins",
        smallMargins: "Small",
        mediumMargins: "Medium",
        largeMargins: "Large",
        lineSpacing: "Line Spacing",

        // Common
        dragDropPdfToEdit: "Drag and drop a PDF file to edit",
        orClickToSelectPdfToEdit: "or click to select a PDF file",

        deleteSelectedPages: "Delete Selected Pages",
        selectPagesToDelete: "Select Pages to Delete",
        selectedPdf: "Selected PDF",

        dragDropSinglePdf: "Drag and drop a PDF file here",
        orClickToSelectSinglePdf: "or click to select a PDF file",
        splitOptions: "Split Options",
        splitByPages: "Split by page ranges",
        splitEvery: "Split every N pages",
        splitIndividual: "Split into individual pages",

        textEditor: "Text Editor",
        createPdf: "Create PDF",
        lineSpacing: "Line Spacing",


        allowCopying: "Allow copying text",
        allowModifying: "Allow modifying",

        dragDropPdfToEdit: "Drag and drop a PDF file to edit",
        orClickToSelectPdfToEdit: "or click to select a PDF file",
        selectPagesToDelete: "Select Pages to Delete",
        selectAll: "Select All",
        deselectAll: "Deselect All",
        orEnterPageNumbers: "Or enter page numbers",
        deleteSelectedPages: "Delete Selected Pages",
        pagesSelected: "Pages Selected:",
        batchConvert: "Batch Convert",
        customSettings: "Custom Settings",



        // Welcome Screen
        welcomeTitle: "Welcome to Question Generator",
        welcomeSubtitle: "Generate multiple-choice and true/false questions from your educational content",
        chooseQuestionType: "Choose Question Type",
        multipleChoice: "Multiple Choice (MCQ)",
        multipleChoiceDesc: "Generate questions with multiple options",
        trueFalse: "True/False (TF)",
        trueFalseDesc: "Generate true or false questions",
        
        // Content Input
        addYourContent: "Add Your Content",
        typeText: "Type Text",
        uploadFile: "Upload File",
        uploadImage: "Upload Image",
        
        // AI Model Selection
        aiModelSelection: "AI Model Selection",
        aiModelSelectionDesc: "Choose your preferred AI model for question generation",
        preferredAiModel: "Preferred AI Model",
        autoBestAvailable: "Auto (Best Available)",
        checkingAvailability: "Checking availability...",
        modelStatusWillAppear: "Model status will appear here",
        modelAvailable: "Model available",
        modelRateLimited: "Rate Limited",
        modelTemporarilyUnavailable: "Model temporarily unavailable due to rate limits",
        
        // Model Management
        modelManagement: "Model Management",
        modelManagementDesc: "Manage your AI models and API configuration",
        aiModels: "AI Models",
        addModel: "Add Model",
        addNewAiModel: "Add new AI model",
        removeModel: "Remove Model",
        deleteExistingModel: "Delete existing model",

        toolsTesting: "Tools & Testing",
        testModels: "Test Models",
        verifyFunctionality: "Verify functionality",
        apiKey: "API Key",
        manageCredentials: "Manage credentials",
        
        // Question Settings removed - now using rate limits from admin panel
        
        // Content Screen
        addContent: "Add Content",
        back: "Back",
        enterContentPlaceholder: "Enter your educational content here...",
        generateQuestions: "Generate Questions",
        dragDropFile: "Drag and drop your file here or click to browse",
        supportedFiles: "Supported: PDF, DOCX, DOC, TXT",
        dragDropImage: "Drag and drop your image here or click to browse",
        supportedImages: "Supported: JPG, PNG, BMP, TIFF",

        // Mind Map
        generateMindMap: "Generate Mind Map",
        enterContentForMindMap: "Enter your content to create a mind map...",
        mindMapVisualization: "Mind Map Visualization",
        mindMapPlaceholder: "Your mind map will appear here after generation",

        // Processing Screen
        aiQuestionGenerator: "AI Question Generator",
        aiMindMapGenerator: "AI Mind Map Generator",
        initializingAiModels: "Initializing AI models...",
        initializingAiModelsForMindMap: "Initializing AI models for mind map generation...",
        generatingMindMap: "Generating Mind Map",
        calculating: "Calculating...",
        analyzingContent: "Analyzing Content",
        processingAiModels: "Processing AI Models",
        generatingQuestions: "Generating Questions",
        finalizing: "Finalizing",
        
        // Questions Display
        generatedQuestions: "Generated Questions",
        startQuiz: "Start Quiz",
        showQuestionsWithAnswers: "Show Questions with Answers",
        showAnswers: "Show Answers",
        hideAnswers: "Hide Answers",
        exportPdf: "Export PDF",
        mainMenu: "Main Menu",
        
        // Quiz Screen
        question: "Question",
        of: "of",
        score: "Score",
        submitAnswer: "Submit Answer",
        nextQuestion: "Next Question",
        finishQuiz: "Finish Quiz",
        
        // Results Screen
        quizResults: "Quiz Results",
        
        // Buttons and Actions
        close: "Close",
        cancel: "Cancel",
        confirm: "Confirm",
        save: "Save",
        delete: "Delete",
        edit: "Edit",
        refresh: "Refresh",
        
        // Model Management Dialogs
        addNewModel: "Add New AI Model",
        modelId: "Model ID",
        modelIdPlaceholder: "e.g., openai/gpt-4:free",
        modelIdHelp: "Enter the full model identifier (provider/model-name:tier)",
        displayName: "Display Name",
        displayNamePlaceholder: "e.g., GPT-4 (Free)",
        displayNameHelp: "Friendly name to display in the dropdown",
        description: "Description (Optional)",
        descriptionPlaceholder: "e.g., Advanced reasoning model",
        descriptionHelp: "Brief description of the model's capabilities",
        
        removeAiModel: "Remove AI Model",
        selectModelToRemove: "Select Model to Remove:",
        chooseModelToRemove: "Choose a model to remove...",
        removeWarning: "⚠️ Warning: This will permanently remove the model from your list. You can remove ANY model including all default models.",
        
        allAiModels: "All AI Models",
        loadingModels: "Loading models...",
        
        modelTestingSimulation: "Model Testing & Simulation",
        testContent: "Test Content",
        testContentPlaceholder: "Enter test content for question generation...",
        testContentDefault: "The human heart has four chambers: two atria and two ventricles. Blood flows from the right atrium to the right ventricle, then to the lungs for oxygenation.",
        questionType: "Question Type",
        questionCount: "Question Count",
        startTestingAllModels: "Start Testing All Models",
        stopTesting: "Stop Testing",
        clearRateLimits: "Clear Rate Limits",
        clickStartTesting: "Click \"Start Testing\" to test all available models",
        exportResults: "Export Results",
        
        // API Key Manager
        apiKeyManager: "API Key Manager",
        currentApiKey: "Current API Key Status",
        newApiKey: "Update API Key",
        enterNewApiKey: "New OpenRouter API Key:",
        updateApiKey: "Update API Key",
        apiKeyUpdated: "API key updated successfully!",
        testResults: "Test Results",
        quickInstructions: "Quick Instructions",

        // Modal Dialog Actions
        close: "Close",
        cancel: "Cancel",
        refresh: "Refresh",

        // Add Model Dialog
        addNewAiModel: "Add New AI Model",
        modelId: "Model ID:",
        modelIdHelp: "Enter the full model identifier (provider/model-name:tier)",
        displayName: "Display Name:",
        displayNameHelp: "Friendly name to display in the dropdown",
        descriptionOptional: "Description (Optional):",
        descriptionHelp: "Brief description of the model's capabilities",

        // Remove Model Dialog
        removeAiModel: "Remove AI Model",
        selectModelToRemove: "Select Model to Remove:",
        chooseModelToRemove: "Choose a model to remove...",
        removeModelWarning: "⚠️ Warning: This will permanently remove the model from your list. You can remove ANY model including all default models.",

        // View Models Dialog
        allAiModels: "All AI Models",
        loadingModels: "Loading models...",

        // Test Models Dialog
        modelTestingSimulation: "Model Testing & Simulation",
        testContent: "Test Content:",
        testContentPlaceholder: "Enter test content for question generation...",
        multipleChoiceMcq: "Multiple Choice (MCQ)",
        trueFalseTf: "True/False (TF)",
        startTestingAllModels: "Start Testing All Models",
        stopTesting: "Stop Testing",
        clickStartTesting: "Click \"Start Testing\" to test all available models",

        // History & Statistics
        quizHistory: "Quiz History",
        clearHistory: "Clear History",
        dateRange: "Date Range",
        allTypes: "All Types",
        allTime: "All Time",
        today: "Today",
        yesterday: "Yesterday",
        thisWeek: "This Week",
        thisMonth: "This Month",
        totalQuizzes: "Total Quizzes",
        averageScore: "Average Score",
        averageTime: "Average Time",
        loadingQuizHistory: "Loading quiz history...",
        savedQuizzes: "Saved Quizzes",
        completedQuizzes: "Completed Quizzes",
        loadingSavedQuizzes: "Loading saved quizzes...",
        takeQuiz: "Take Quiz",
        retakeQuiz: "Retake Quiz",
        downloadPdf: "Download PDF",
        clearQuiz: "Clear",
        clearAll: "Clear All",
        selectAll: "Select All",
        deselectAll: "Deselect All",
        deleteSelected: "Delete Selected",
        deleteSavedQuiz: "Delete",
        noSavedQuizzes: "No saved quizzes found",
        source: "Source",
        clearAllSaved: "Clear All",
        saveQuizTitle: "Save Quiz",
        saveQuizSubtitle: "Give your quiz a memorable name",
        quizNameLabel: "Quiz Name:",
        quizNameHint: "Leave empty to use default name",
        saveQuiz: "Save Quiz",

        // Saved Mind Maps
        openSavedMindMaps: "View Saved Maps",
        noSavedMaps: "No Saved Mind Maps",
        noSavedMapsDesc: "Create and save mind maps to access them here",
        createMindMap: "Create Mind Map",
        saveMindMapTitle: "Save Mind Map",
        saveMindMapDesc: "Enter a name for your mind map to save it for later access.",
        mindMapNameLabel: "Mind Map Name:",
        mindMapNameHint: "Choose a descriptive name to easily find it later",
        saveMindMap: "Save Mind Map",
        cancel: "Cancel",
        cancel: "Cancel",
        source: "Source",
        type: "Type",
        questions: "Questions",
        quizSummaryText: "You are about to save a",
        quizSummaryWith: "quiz with",
        quizSummaryQuestions: "questions from",

        statistics: "Statistics",
        exportStats: "Export Stats",
        overallScore: "Overall Score",
        acrossAllQuizzes: "Across all quizzes",
        currentStreak: "Current Streak",
        daysInARow: "Days in a row",
        performanceMetrics: "Performance Metrics",
        totalQuestions: "Total Questions",
        correctAnswers: "Correct Answers",
        incorrectAnswers: "Incorrect Answers",
        questionTypes: "Question Types",
        mcqQuizzes: "MCQ Quizzes",
        trueFalseQuizzes: "True/False Quizzes",
        activity: "Activity",
        quizzesToday: "Quizzes Today",

        // Real-time features

        lastUpdated: "Last updated:",

        // Quiz Results
        quizResults: "Quiz Results",
        newQuiz: "New Quiz",
        takeAnotherQuiz: "Take Another Quiz",
        saveQuiz: "Save Quiz for Later",

        // Dynamic content translations
        score: "Score",
        correct: "Correct",
        duration: "Duration",
        questions: "Questions",

        // Status Messages
        loading: "Loading...",
        success: "Success",
        error: "Error",
        warning: "Warning",
        info: "Info"
    },
    
    ar: {
        // Header
        appTitle: "مولد الأسئلة متعددة الخيارات والصح والخطأ",
        switchLanguage: "تغيير اللغة",
        history: "التاريخ",
        statistics: "الإحصائيات",
        backToMain: "العودة للرئيسية",
        
        // Home Screen
        appMainTitle: "إكس-شتاين",
        appMainSubtitle: "محرك الدراسة الشامل المدعوم بالذكاء الاصطناعي",
        desktopUser: "مستخدم المكتب",
        notSignedIn: "غير مسجل الدخول",

        // Services
        quizGenerator: "مولد الأسئلة",
        quizGeneratorDesc: "إنشاء أسئلة متعددة الخيارات والصح والخطأ من أي محتوى",
        pdfEditor: "محرر بي دي اف",
        pdfEditorDesc: "مجموعة أدوات شاملة لمعالجة ملفات الـ بي دي اف مع ميزات متقدمة",
        pdfEditorSubtitle: "مجموعة أدوات شاملة لمعالجة بي دي اف",
        imageToPdf: "تحويل الصور إلى بي دي اف",
        imageToPdfDesc: "تحويل الصور إلى مستندات بي دي اف مع خيارات متقدمة",
        mergePdf: "دمج بي دي اف",
        splitPdf: "تقسيم بي دي اف",

        textToPdf: "نص إلى بي دي اف",
        deletePages: "حذف الصفحات",

        textTools: "أدوات النصوص",
        textToolsDesc: "أدوات متقدمة لمعالجة وتحليل النصوص",
        textToolsSubtitle: "تحليل النصوص والتصور المدعوم بالذكاء الاصطناعي",
        mindMapDesc: "إنشاء خرائط ذهنية مدعومة بالذكاء الاصطناعي من أي محتوى",
        mindMapSubtitle: "إنشاء خرائط ذهنية مدعومة بالذكاء الاصطناعي من أي محتوى",
        visualization: "التصور",
        studyTools: "أدوات الدراسة",
        studyToolsDesc: "البطاقات التعليمية والتكرار المتباعد وتخطيط الدراسة",
        aiAssistant: "المساعد الذكي",
        aiAssistantDesc: "تحدث مع الذكاء الاصطناعي للحصول على المساعدة والشروحات",
        savedMindMaps: "الخرائط الذهنية المحفوظة",
        savedMindMapsDesc: "الوصول إلى وإدارة خرائطك الذهنية المحفوظة",

        // Service Features
        aiPowered: "مدعوم بالذكاء الاصطناعي",
        multipleFormats: "تنسيقات متعددة",
        interactive: "تفاعلي",
        batchConvert: "تحويل مجمع",
        highQuality: "جودة عالية",
        comingSoon: "قريباً",
        multiFormat: "متعدد التنسيقات",
        batchProcess: "معالجة مجمعة",
        ocr: "استخراج النص",
        analysis: "تحليل",
        mindMap: "خريطة ذهنية",
        spacedRepetition: "التكرار المتباعد",
        organized: "منظم",
        accessible: "قابل للوصول",
        manageable: "قابل للإدارة",
        analytics: "التحليلات",
        multiModel: "متعدد النماذج",
        contextAware: "مدرك للسياق",

        // Actions
        launch: "تشغيل",
        soon: "قريباً",
        home: "الرئيسية",

        // Stats
        questionsGenerated: "الأسئلة المُنشأة",
        filesProcessed: "الملفات المُعالجة",
        mindMapsGenerated: "الخرائط الذهنية المُنشأة",

        // Image to PDF Converter
        dragDropImages: "اسحب وأفلت الصور هنا",
        orClickToSelect: "أو انقر لاختيار الملفات",
        supportedFormats: "التنسيقات المدعومة:",
        selectedImages: "الصور المحددة",
        clearAll: "مسح الكل",
        pdfSettings: "إعدادات بي دي اف",
        pdfSettingsDesc: "تكوين مخرجات بي دي اف الخاصة بك",
        imageQuality: "جودة الصورة",
        highQuality: "جودة عالية",
        mediumQuality: "جودة متوسطة",
        lowQuality: "جودة منخفضة",
        pageSize: "حجم الصفحة",
        a4Size: "A4",
        letterSize: "Letter",
        legalSize: "Legal",
        autoSize: "تلقائي (ملائمة الصورة)",

        margins: "الهوامش",
        noMargins: "بدون هوامش",
        smallMargins: "صغيرة",
        mediumMargins: "متوسطة",
        largeMargins: "كبيرة",
        convertToPdf: "تحويل إلى بي دي اف",
        convertToPdfDesc: "إنشاء مستند بي دي اف الخاص بك",
        generatePdf: "إنشاء بي دي اف",
        imagesSelected: "الصور المحددة:",
        estimatedSize: "الحجم المقدر:",

        // PDF Editor Features
        dragDropPdfs: "اسحب وأفلت ملفات بي دي اف هنا",
        orClickToSelectPdfs: "أو انقر لاختيار ملفات بي دي اف",
        selectedPdfs: "ملفات بي دي اف المحددة",
        pdfsSelected: "ملفات بي دي اف المحددة:",
        mergePdfs: "دمج ملفات بي دي اف",
        maintainBookmarks: "الاحتفاظ بالإشارات المرجعية",
        maintainBookmarksHint: "الحفاظ على إشارات التنقل من ملفات بي دي اف المصدر",
        outputQuality: "جودة الإخراج",
        balancedQuality: "متوازن",
        compressedQuality: "مضغوط (ملف أصغر)",
        addTableOfContents: "إضافة جدول المحتويات",
        addTableOfContentsHint: "إنشاء صفحة جدول محتويات بأسماء الملفات المصدر",
        addPageNumbers: "إضافة أرقام الصفحات",
        addPageNumbersHint: "إضافة أرقام الصفحات إلى ملف بي دي اف المدمج",

        // Split PDF
        splitMethod: "طريقة التقسيم",
        splitByPageRanges: "تقسيم حسب نطاقات الصفحات",
        splitEveryNPages: "تقسيم كل N صفحات",
        extractSpecificPages: "استخراج صفحات محددة",
        pageRanges: "نطاقات الصفحات",
        pageRangesHint: "أدخل نطاقات الصفحات مفصولة بفواصل (مثل: 1-5, 8-10, 15)",
        pagesPerFile: "صفحات لكل ملف",
        pagesPerFileHint: "عدد الصفحات في كل ملف مقسم",
        extractPages: "الصفحات المراد استخراجها",
        extractPagesHint: "أدخل الصفحات المحددة للاستخراج (مثل: 1, 3, 5-7)",



        // Delete Pages
        pagesToDelete: "الصفحات المراد حذفها",
        pagesToDeleteHint: "أدخل أرقام الصفحات المراد حذفها (مثل: 1, 3, 5-7, 10)",
        deleteWarning: "تحذير",
        deleteWarningMessage: "لا يمكن استرداد الصفحات المحذوفة. تأكد من عمل نسخة احتياطية من ملف بي دي اف الأصلي.",

        // Text to PDF
        textEditor: "محرر النصوص",
        margins: "الهوامش",
        smallMargins: "صغيرة",
        mediumMargins: "متوسطة",
        largeMargins: "كبيرة",
        lineSpacing: "تباعد الأسطر",

        dragDropSinglePdf: "اسحب وأفلت ملف بي دي اف هنا",
        orClickToSelectSinglePdf: "أو انقر لاختيار ملف بي دي اف",
        splitOptions: "خيارات التقسيم",
        splitByPages: "تقسيم حسب نطاقات الصفحات",
        splitEvery: "تقسيم كل N صفحات",
        splitIndividual: "تقسيم إلى صفحات فردية",

        textEditor: "محرر النصوص",
        createPdf: "إنشاء بي دي اف",
        lineSpacing: "تباعد الأسطر",

        // Common
        dragDropPdfToEdit: "اسحب وأفلت ملف بي دي اف للتحرير",
        orClickToSelectPdfToEdit: "أو انقر لاختيار ملف بي دي اف",

        deleteSelectedPages: "حذف الصفحات المحددة",
        selectPagesToDelete: "اختر الصفحات المراد حذفها",
        selectedPdf: "ملف بي دي اف المحدد",


        dragDropPdfToEdit: "اسحب وأفلت ملف بي دي اف للتحرير",
        orClickToSelectPdfToEdit: "أو انقر لاختيار ملف بي دي اف",
        selectPagesToDelete: "اختر الصفحات للحذف",
        selectAll: "تحديد الكل",
        deselectAll: "إلغاء تحديد الكل",
        orEnterPageNumbers: "أو أدخل أرقام الصفحات",
        deleteSelectedPages: "حذف الصفحات المحددة",
        pagesSelected: "الصفحات المحددة:",
        batchConvert: "تحويل مجمع",
        customSettings: "إعدادات مخصصة",



        // Welcome Screen
        welcomeTitle: "مرحباً بك في مولد الأسئلة",
        welcomeSubtitle: "قم بإنشاء أسئلة متعددة الخيارات والصح والخطأ من محتواك التعليمي",
        chooseQuestionType: "اختر نوع السؤال",
        multipleChoice: "متعدد الخيارات",
        multipleChoiceDesc: "إنشاء أسئلة بخيارات متعددة",
        trueFalse: "صح أو خطأ",
        trueFalseDesc: "إنشاء أسئلة صح أو خطأ",
        
        // Content Input
        addYourContent: "أضف محتواك",
        typeText: "اكتب النص",
        uploadFile: "رفع ملف",
        uploadImage: "رفع صورة",
        
        // AI Model Selection
        aiModelSelection: "اختيار نموذج الذكاء الاصطناعي",
        aiModelSelectionDesc: "اختر نموذج الذكاء الاصطناعي المفضل لديك لإنشاء الأسئلة",
        preferredAiModel: "نموذج الذكاء الاصطناعي المفضل",
        autoBestAvailable: "تلقائي (الأفضل المتاح)",
        checkingAvailability: "فحص التوفر...",
        modelStatusWillAppear: "ستظهر حالة النموذج هنا",
        modelAvailable: "النموذج متاح",
        modelRateLimited: "محدود المعدل",
        modelTemporarilyUnavailable: "النموذج غير متاح مؤقتاً بسبب حدود المعدل",
        
        // Model Management
        modelManagement: "إدارة النماذج",
        modelManagementDesc: "إدارة نماذج الذكاء الاصطناعي وإعدادات API",
        aiModels: "نماذج الذكاء الاصطناعي",
        addModel: "إضافة نموذج",
        addNewAiModel: "إضافة نموذج ذكاء اصطناعي جديد",
        removeModel: "حذف نموذج",
        deleteExistingModel: "حذف نموذج موجود",

        toolsTesting: "الأدوات والاختبار",
        testModels: "اختبار النماذج",
        verifyFunctionality: "التحقق من الوظائف",
        apiKey: "مفتاح API",
        manageCredentials: "إدارة بيانات الاعتماد",
        
        // Question Settings removed - now using rate limits from admin panel
        
        // Content Screen
        addContent: "إضافة محتوى",
        back: "رجوع",
        enterContentPlaceholder: "أدخل محتواك التعليمي هنا...",
        generateQuestions: "إنشاء الأسئلة",
        dragDropFile: "اسحب وأفلت ملفك هنا أو انقر للتصفح",
        supportedFiles: "المدعوم: بي دي اف, DOCX, DOC, TXT",
        dragDropImage: "اسحب وأفلت صورتك هنا أو انقر للتصفح",
        supportedImages: "المدعوم: JPG, PNG, BMP, TIFF",

        // Mind Map
        generateMindMap: "إنشاء خريطة ذهنية",
        enterContentForMindMap: "أدخل محتواك لإنشاء خريطة ذهنية...",
        mindMapVisualization: "تصور الخريطة الذهنية",
        mindMapPlaceholder: "ستظهر خريطتك الذهنية هنا بعد الإنشاء",

        // Processing Screen
        aiQuestionGenerator: "مولد الأسئلة بالذكاء الاصطناعي",
        aiMindMapGenerator: "مولد الخرائط الذهنية بالذكاء الاصطناعي",
        initializingAiModels: "تهيئة نماذج الذكاء الاصطناعي...",
        initializingAiModelsForMindMap: "تهيئة نماذج الذكاء الاصطناعي لإنشاء الخرائط الذهنية...",
        generatingMindMap: "إنشاء الخريطة الذهنية",
        calculating: "جاري الحساب...",
        analyzingContent: "تحليل المحتوى",
        processingAiModels: "معالجة نماذج الذكاء الاصطناعي",
        generatingQuestions: "إنشاء الأسئلة",
        finalizing: "الانتهاء",
        
        // Questions Display
        generatedQuestions: "الأسئلة المُنشأة",
        startQuiz: "بدء الاختبار",
        showQuestionsWithAnswers: "عرض الأسئلة مع الإجابات",
        showAnswers: "عرض الإجابات",
        hideAnswers: "إخفاء الإجابات",
        exportPdf: "تصدير بي دي اف",
        mainMenu: "القائمة الرئيسية",
        
        // Quiz Screen
        question: "السؤال",
        of: "من",
        score: "النتيجة",
        submitAnswer: "إرسال الإجابة",
        nextQuestion: "السؤال التالي",
        finishQuiz: "إنهاء الاختبار",
        
        // Results Screen
        quizResults: "نتائج الاختبار",
        
        // Buttons and Actions
        close: "إغلاق",
        cancel: "إلغاء",
        confirm: "تأكيد",
        save: "حفظ",
        delete: "حذف",
        edit: "تعديل",
        refresh: "تحديث",
        
        // Model Management Dialogs
        addNewAiModel: "إضافة نموذج ذكاء اصطناعي جديد",
        modelId: "معرف النموذج:",
        modelIdPlaceholder: "مثال: openai/gpt-4:free",
        modelIdHelp: "أدخل معرف النموذج الكامل (المزود/اسم-النموذج:المستوى)",
        displayName: "اسم العرض:",
        displayNamePlaceholder: "مثال: GPT-4 (مجاني)",
        displayNameHelp: "اسم ودود للعرض في القائمة المنسدلة",
        descriptionOptional: "الوصف (اختياري):",
        descriptionPlaceholder: "مثال: نموذج تفكير متقدم",
        descriptionHelp: "وصف موجز لقدرات النموذج",

        removeAiModel: "حذف نموذج الذكاء الاصطناعي",
        selectModelToRemove: "اختر النموذج المراد حذفه:",
        chooseModelToRemove: "اختر نموذجاً لحذفه...",
        removeModelWarning: "⚠️ تحذير: سيؤدي هذا إلى حذف النموذج نهائياً من قائمتك. يمكنك حذف أي نموذج بما في ذلك جميع النماذج الافتراضية.",
        
        allAiModels: "جميع نماذج الذكاء الاصطناعي",
        loadingModels: "تحميل النماذج...",
        
        modelTestingSimulation: "اختبار ومحاكاة النماذج",
        testContent: "محتوى الاختبار:",
        testContentPlaceholder: "أدخل محتوى الاختبار لإنشاء الأسئلة...",
        testContentDefault: "القلب البشري له أربع حجرات: أذينان وبطينان. يتدفق الدم من الأذين الأيمن إلى البطين الأيمن، ثم إلى الرئتين للأكسجة.",
        questionType: "نوع السؤال:",
        multipleChoiceMcq: "متعدد الخيارات (MCQ)",
        trueFalseTf: "صح أو خطأ (TF)",
        questionCount: "عدد الأسئلة:",
        startTestingAllModels: "بدء اختبار جميع النماذج",
        stopTesting: "إيقاف الاختبار",
        clearRateLimits: "مسح حدود المعدل",
        clickStartTesting: "انقر \"بدء الاختبار\" لاختبار جميع النماذج المتاحة",
        exportResults: "تصدير النتائج",
        
        // API Key Manager
        apiKeyManager: "مدير مفتاح API",
        currentApiKey: "حالة مفتاح API الحالي",
        newApiKey: "تحديث مفتاح API",
        enterNewApiKey: "مفتاح OpenRouter API الجديد:",
        updateApiKey: "تحديث مفتاح API",
        apiKeyUpdated: "تم تحديث مفتاح API بنجاح!",
        testResults: "نتائج الاختبار",
        quickInstructions: "تعليمات سريعة",

        // History & Statistics
        quizHistory: "تاريخ الاختبارات",
        clearHistory: "مسح التاريخ",
        dateRange: "نطاق التاريخ",
        allTypes: "جميع الأنواع",
        allTime: "كل الأوقات",
        today: "اليوم",
        yesterday: "أمس",
        thisWeek: "هذا الأسبوع",
        thisMonth: "هذا الشهر",
        totalQuizzes: "إجمالي الاختبارات",
        averageScore: "متوسط النتيجة",
        averageTime: "متوسط الوقت",
        loadingQuizHistory: "تحميل تاريخ الاختبارات...",
        savedQuizzes: "الاختبارات المحفوظة",
        completedQuizzes: "الاختبارات المكتملة",
        loadingSavedQuizzes: "تحميل الاختبارات المحفوظة...",
        takeQuiz: "خذ الاختبار",
        retakeQuiz: "إعادة الاختبار",
        downloadPdf: "تحميل بي دي اف",
        clearQuiz: "حذف",
        clearAll: "مسح الكل",
        selectAll: "تحديد الكل",
        deselectAll: "إلغاء التحديد",
        deleteSelected: "حذف المحدد",
        deleteSavedQuiz: "حذف",
        noSavedQuizzes: "لا توجد اختبارات محفوظة",
        source: "المصدر",
        clearAllSaved: "مسح الكل",
        saveQuizTitle: "حفظ الاختبار",
        saveQuizSubtitle: "أعط اختبارك اسماً لا يُنسى",
        quizNameLabel: "اسم الاختبار:",
        quizNameHint: "اتركه فارغاً لاستخدام الاسم الافتراضي",
        saveQuiz: "حفظ الاختبار",

        // Saved Mind Maps
        openSavedMindMaps: "عرض الخرائط المحفوظة",
        noSavedMaps: "لا توجد خرائط ذهنية محفوظة",
        noSavedMapsDesc: "أنشئ واحفظ خرائط ذهنية للوصول إليها هنا",
        createMindMap: "إنشاء خريطة ذهنية",
        saveMindMapTitle: "حفظ الخريطة الذهنية",
        saveMindMapDesc: "أدخل اسماً للخريطة الذهنية لحفظها للوصول إليها لاحقاً.",
        mindMapNameLabel: "اسم الخريطة الذهنية:",
        mindMapNameHint: "اختر اسماً وصفياً للعثور عليها بسهولة لاحقاً",
        saveMindMap: "حفظ الخريطة الذهنية",
        cancel: "إلغاء",
        cancel: "إلغاء",
        source: "المصدر",
        type: "النوع",
        questions: "الأسئلة",
        quizSummaryText: "أنت على وشك حفظ اختبار",
        quizSummaryWith: "يحتوي على",
        quizSummaryQuestions: "أسئلة من",

        statistics: "الإحصائيات",
        exportStats: "تصدير الإحصائيات",
        overallScore: "النتيجة الإجمالية",
        acrossAllQuizzes: "عبر جميع الاختبارات",
        currentStreak: "السلسلة الحالية",
        daysInARow: "أيام متتالية",
        performanceMetrics: "مقاييس الأداء",
        totalQuestions: "إجمالي الأسئلة",
        correctAnswers: "الإجابات الصحيحة",
        incorrectAnswers: "الإجابات الخاطئة",
        questionTypes: "أنواع الأسئلة",
        mcqQuizzes: "اختبارات متعددة الخيارات",
        trueFalseQuizzes: "اختبارات صح أو خطأ",
        activity: "النشاط",
        quizzesToday: "اختبارات اليوم",

        // Real-time features

        lastUpdated: "آخر تحديث:",

        // Quiz Results
        quizResults: "نتائج الاختبار",
        newQuiz: "اختبار جديد",
        takeAnotherQuiz: "إجراء اختبار آخر",
        saveQuiz: "حفظ الاختبار للاحقاً",

        // Dynamic content translations
        score: "النتيجة",
        correct: "صحيح",
        duration: "المدة",
        questions: "الأسئلة",

        // Status Messages
        loading: "جاري التحميل...",
        success: "نجح",
        error: "خطأ",
        warning: "تحذير",
        info: "معلومات"
    }
};

// Current language state
let currentLanguage = localStorage.getItem('language') || 'en';

// Translation function
function t(key) {
    return translations[currentLanguage][key] || translations['en'][key] || key;
}

// Update all text elements with translations
function updateLanguage() {
    // Update language toggle button (main header)
    const languageToggle = document.getElementById('languageToggle');
    if (languageToggle) {
        const languageText = languageToggle.querySelector('.language-text');
        if (languageText) {
            languageText.textContent = currentLanguage === 'en' ? 'عربي' : 'English';
        }
        languageToggle.title = t('switchLanguage');
    }

    // Update home language toggle button
    const homeLanguageToggle = document.getElementById('homeLanguageToggle');
    if (homeLanguageToggle) {
        const homeLanguageText = homeLanguageToggle.querySelector('.language-text');
        if (homeLanguageText) {
            homeLanguageText.textContent = currentLanguage === 'en' ? 'عربي' : 'English';
        }
        homeLanguageToggle.title = t('switchLanguage');
    }

    // Update document language but keep LTR layout, use class for Arabic font
    document.documentElement.dir = 'ltr'; // Always keep LTR layout
    document.documentElement.lang = currentLanguage;

    // Add/remove Arabic class for font styling without changing layout
    if (currentLanguage === 'ar') {
        document.documentElement.classList.add('arabic-lang');
    } else {
        document.documentElement.classList.remove('arabic-lang');
    }

    // Update all elements with data-translate attribute
    document.querySelectorAll('[data-translate]').forEach(element => {
        const key = element.getAttribute('data-translate');

        // Special handling for user name - don't translate if it's showing an actual user name
        if (element.classList.contains('user-name')) {
            const currentText = element.textContent;
            const storedUser = element.getAttribute('data-current-user');

            // If we have a stored user name and it's not the default "Not Signed In", preserve it
            if (storedUser && storedUser !== 'Not Signed In' && storedUser !== 'غير مسجل الدخول') {
                element.textContent = storedUser;
                console.log('✅ Preserved user name during language change:', storedUser);
            } else {
                // Only translate if it's currently showing the default "Not Signed In" text
                if (currentText === 'Not Signed In' || currentText === 'غير مسجل الدخول') {
                    element.textContent = t(key);
                }
            }
            return;
        }

        if (element.tagName === 'INPUT' && (element.type === 'text' || element.type === 'textarea')) {
            element.placeholder = t(key);
        } else if (element.tagName === 'TEXTAREA') {
            element.placeholder = t(key);
        } else {
            element.textContent = t(key);
        }
    });

    // Update specific elements by ID or class
    updateElementText('#historyBtn', '', 'title', t('history'));
    updateElementText('#statsBtn', '', 'title', t('statistics'));
    updateElementText('#headerBackBtn', '', 'title', t('backToMain'));

    // Update model select auto option
    const autoOption = document.querySelector('#modelSelect option[value="auto"]');
    if (autoOption) {
        autoOption.textContent = t('autoBestAvailable');
    }

    // Update status text if visible
    const statusText = document.querySelector('.status-text');
    if (statusText && statusText.textContent.includes('Checking') || statusText.textContent.includes('فحص')) {
        statusText.textContent = t('checkingAvailability');
    }

    const statusDescription = document.querySelector('.status-description');
    if (statusDescription && (statusDescription.textContent.includes('Model status') || statusDescription.textContent.includes('ستظهر'))) {
        statusDescription.textContent = t('modelStatusWillAppear');
    }

    // Update dynamic content that might be set by JavaScript
    updateDynamicContent();

    // Save language preference
    localStorage.setItem('language', currentLanguage);
}

// Update dynamic content that gets set by JavaScript
function updateDynamicContent() {
    // Update content screen title if it exists and has been set
    const contentScreenTitle = document.getElementById('contentScreenTitle');
    if (contentScreenTitle && contentScreenTitle.textContent.includes('Add Content')) {
        // Check if it's for MCQ or TF
        if (contentScreenTitle.textContent.includes('MCQ')) {
            contentScreenTitle.textContent = t('addContent') + ' ' + t('multipleChoice');
        } else if (contentScreenTitle.textContent.includes('TF')) {
            contentScreenTitle.textContent = t('addContent') + ' ' + t('trueFalse');
        } else {
            contentScreenTitle.textContent = t('addContent');
        }
    }

    // Update question number and count in quiz if visible
    const questionNumber = document.getElementById('questionNumber');
    if (questionNumber && questionNumber.textContent.includes('Question')) {
        const match = questionNumber.textContent.match(/(\d+)/);
        if (match) {
            questionNumber.innerHTML = `<span data-translate="question">${t('question')}</span> ${match[1]}`;
        }
    }

    const questionCount = document.getElementById('questionCount');
    if (questionCount && questionCount.textContent.includes('of')) {
        const match = questionCount.textContent.match(/(\d+)/);
        if (match) {
            questionCount.innerHTML = `<span data-translate="of">${t('of')}</span> ${match[1]}`;
        }
    }

    // Update score label
    const scoreElement = document.querySelector('.quiz-score span:first-child');
    if (scoreElement && scoreElement.textContent.includes('Score')) {
        scoreElement.textContent = t('score') + ': ';
    }
}

// Helper function to update element text
function updateElementText(selector, text, attribute = null, value = null) {
    const element = document.querySelector(selector);
    if (element) {
        if (attribute) {
            element.setAttribute(attribute, value);
        } else if (text) {
            element.textContent = text;
        }
    }
}

// Toggle language function
function toggleLanguage() {
    currentLanguage = currentLanguage === 'en' ? 'ar' : 'en';
    localStorage.setItem('language', currentLanguage);
    updateLanguage();
}

// Initialize language system
function initializeLanguage() {
    updateLanguage();
    
    // Add event listener to language toggle button
    const languageToggle = document.getElementById('languageToggle');
    if (languageToggle) {
        languageToggle.addEventListener('click', toggleLanguage);
    }
}

// Export functions for use in other files
window.t = t;
window.updateLanguage = updateLanguage;
window.updateDynamicContent = updateDynamicContent;
window.toggleLanguage = toggleLanguage;
window.initializeLanguage = initializeLanguage;
window.currentLanguage = () => currentLanguage;
