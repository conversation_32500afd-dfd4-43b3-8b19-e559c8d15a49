const { Telegraf, Markup } = require('telegraf');
const fs = require('fs');
const path = require('path');
const logger = require('../utils/logger');
const apiService = require('./apiService');
const fileService = require('./fileService');
const database = require('../database/database');
const botMessagesManager = require('./botMessagesManager');
const rateLimitService = require('./rateLimitService');

class TelegramBotService {
    constructor() {
        this.bot = null;
        this.isRunning = false;
        this.botToken = process.env.TELEGRAM_BOT_TOKEN;
        this.adminIds = (process.env.ADMIN_IDS || '').split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
        this.requiredChannelId = process.env.REQUIRED_CHANNEL_ID;

        // User session management
        this.userSessions = new Map();

        // User question type preferences
        this.userQuestionTypes = new Map();

        // IPC handlers for AI generation
        this.ipcHandlers = null;

        // Statistics
        this.stats = {
            totalUsers: 0,
            activeUsers: 0,
            questionsGenerated: 0,
            filesProcessed: 0,
            startTime: Date.now()
        };
    }

    /**
     * Set IPC handlers instance for AI generation
     */
    setIPCHandlers(ipcHandlers) {
        this.ipcHandlers = ipcHandlers;
        logger.info('🔗 IPC handlers set for Telegram bot AI generation');
    }

    /**
     * Reload bot messages from configuration
     */
    async reloadMessages() {
        try {
            // Clear require cache to force fresh reload
            delete require.cache[require.resolve('../config/botMessages')];

            // Reset the singleton before clearing cache
            const botMessagesManager = require('./botMessagesManager');
            if (botMessagesManager.resetInstance) {
                botMessagesManager.resetInstance();
            }

            delete require.cache[require.resolve('./botMessagesManager')];

            // Get a fresh instance and load custom messages
            const freshBotMessagesManager = require('./botMessagesManager');
            await freshBotMessagesManager.loadCustomMessages();

            logger.info('✅ Telegram bot messages reloaded');
            return { success: true };
        } catch (error) {
            logger.error('❌ Error reloading Telegram bot messages:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Get a message from the messages manager
     */
    getMessage(keyPath, variables = {}) {
        // Always get a fresh instance to ensure we have the latest messages
        const currentBotMessagesManager = require('./botMessagesManager');
        return currentBotMessagesManager.getMessage(keyPath, variables);
    }

    /**
     * Initialize and start the Telegram bot
     */
    async start() {
        try {
            logger.info('TelegramBot.start() called');
            logger.debug('Bot token exists:', !!this.botToken);
            logger.debug('Bot token configured');

            if (!this.botToken) {
                logger.error('TELEGRAM_BOT_TOKEN is not set');
                throw new Error('TELEGRAM_BOT_TOKEN is not set in environment variables');
            }

            // Stop any existing bot instance first
            if (this.bot && this.isRunning) {
                logger.info('Stopping existing bot instance...');
                await this.stop();
                // Wait a bit for cleanup
                await new Promise(resolve => setTimeout(resolve, 2000));
            }

            this.bot = new Telegraf(this.botToken);

            // Set up middleware
            this.setupMiddleware();

            // Set up command handlers
            this.setupCommands();

            // Set up message handlers
            this.setupMessageHandlers();

            // Set up callback query handlers
            this.setupCallbackHandlers();

            // Handle specific Telegram errors
            this.bot.catch((err, ctx) => {
                logger.error('Bot error:', err);
                if (err.code === 409) {
                    logger.error('Conflict error: Another bot instance is running. Stopping this instance.');
                    this.isRunning = false;
                }
            });

            // Start the bot with retry logic
            await this.startWithRetry();

            logger.success('Telegram bot started successfully');
            logger.success('Bot start completed, isRunning:', this.isRunning);

            // Update statistics
            await this.updateStats();

            logger.debug('Returning success from start method');
            return { success: true, message: 'Bot started successfully' };
        } catch (error) {
            logger.error('Failed to start Telegram bot:', error);
            logger.error('Bot start error:', error.message);
            this.isRunning = false;

            // Handle specific error codes
            if (error.message.includes('409') || error.message.includes('Conflict')) {
                return {
                    success: false,
                    error: '409: Conflict - Another bot instance is running. Please wait a few minutes and try again.'
                };
            }

            return { success: false, error: error.message };
        }
    }

    /**
     * Start bot with retry logic for handling conflicts
     */
    async startWithRetry(maxRetries = 3, delay = 5000) {
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                logger.info(`Starting bot (attempt ${attempt}/${maxRetries})...`);

                // Launch bot (this runs indefinitely, so we don't await it)
                this.bot.launch().catch(error => {
                    logger.error('Bot launch error:', error);
                    this.isRunning = false;
                });

                // Give it a moment to start, then mark as running
                await new Promise(resolve => setTimeout(resolve, 1000));

                this.isRunning = true;
                this.stats.startTime = Date.now(); // Set start time for uptime calculation
                logger.success(`✅ Bot started successfully on attempt ${attempt}! isRunning: ${this.isRunning}`);
                logger.debug('Bot status after successful start:', this.getStatus());
                return;
            } catch (error) {
                if (error.message.includes('409') || error.message.includes('Conflict')) {
                    if (attempt < maxRetries) {
                        logger.warn(`Conflict detected, waiting ${delay/1000}s before retry ${attempt + 1}...`);
                        await new Promise(resolve => setTimeout(resolve, delay));
                        continue;
                    } else {
                        throw new Error('409: Conflict - Another bot instance is running. Please stop other instances and try again.');
                    }
                } else {
                    throw error;
                }
            }
        }
    }

    /**
     * Stop the Telegram bot
     */
    async stop() {
        try {
            if (this.bot && this.isRunning) {
                logger.info('Stopping Telegram bot...');

                // Stop the bot gracefully
                await this.bot.stop();
                this.isRunning = false;

                // Clear the bot instance
                this.bot = null;

                logger.info('Telegram bot stopped successfully');
            }
            return { success: true, message: 'Bot stopped successfully' };
        } catch (error) {
            logger.error('Failed to stop Telegram bot:', error);
            this.isRunning = false;
            this.bot = null;
            return { success: false, error: error.message };
        }
    }

    /**
     * Get bot status
     */
    getStatus() {
        const uptime = this.isRunning && this.stats.startTime ?
            this.formatUptime(Date.now() - this.stats.startTime) : '0h 0m';

        logger.debug('Bot status check:', {
            isRunning: this.isRunning,
            hasBot: !!this.bot,
            startTime: this.stats.startTime,
            uptime: uptime,
            totalUsers: this.stats.totalUsers
        });

        return {
            isRunning: this.isRunning,
            hasBot: !!this.bot,
            botToken: this.botToken ? `${this.botToken.substring(0, 8)}...` : 'Not set',
            uptime: uptime,
            stats: {
                totalUsers: this.stats.totalUsers || 0,
                activeUsers: this.stats.activeUsers || 0,
                questionsGenerated: this.stats.questionsGenerated || 0,
                filesProcessed: this.stats.filesProcessed || 0
            }
        };
    }

    /**
     * Format uptime in human readable format
     */
    formatUptime(milliseconds) {
        const seconds = Math.floor(milliseconds / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);

        if (days > 0) {
            return `${days}d ${hours % 24}h`;
        } else if (hours > 0) {
            return `${hours}h ${minutes % 60}m`;
        } else {
            return `${minutes}m`;
        }
    }

    /**
     * Force stop all bot instances (for conflict resolution)
     */
    async forceStop() {
        try {
            logger.info('Force stopping all bot instances...');

            if (this.bot) {
                try {
                    await this.bot.stop();
                } catch (error) {
                    logger.warn('Error during force stop:', error.message);
                }
                this.bot = null;
            }

            this.isRunning = false;

            // Wait for cleanup
            await new Promise(resolve => setTimeout(resolve, 3000));

            logger.info('Force stop completed');
            return { success: true, message: 'Bot force stopped successfully' };
        } catch (error) {
            logger.error('Failed to force stop bot:', error);
            this.isRunning = false;
            this.bot = null;
            return { success: false, error: error.message };
        }
    }

    /**
     * Set up middleware for user authentication and logging
     */
    setupMiddleware() {
        // User authentication and logging middleware
        this.bot.use(async (ctx, next) => {
            const user = ctx.from;
            const chatId = ctx.chat?.id;
            
            // Enhanced logging for real Telegram users
            const userInfo = {
                id: user.id,
                username: user.username || 'no_username',
                first_name: user.first_name || '',
                last_name: user.last_name || '',
                language_code: user.language_code || 'unknown'
            };

            // Clean, colorful user logging (reduced verbosity)
            const chalk = require('chalk');
            const displayName = userInfo.first_name + (userInfo.last_name ? ` ${userInfo.last_name}` : '');
            const username = userInfo.username ? `@${userInfo.username}` : 'no_username';

            // Only log user activity every 30 seconds to reduce spam
            const userKey = `user_${userInfo.id}`;
            if (!this.lastUserActivityLog) this.lastUserActivityLog = new Map();

            const lastLog = this.lastUserActivityLog.get(userKey);
            if (!lastLog || (Date.now() - lastLog) >= 30000) {
                logger.user(`${displayName} (${username}) ${ctx.updateType}`);
                this.lastUserActivityLog.set(userKey, Date.now());
            }

            // Save/update real Telegram user in database (throttled to reduce spam during quiz sessions)
            const userId = user.id.toString();
            const now = Date.now();

            if (!this.lastUserSaveMiddleware) this.lastUserSaveMiddleware = new Map();

            const lastSave = this.lastUserSaveMiddleware.get(userId);
            if (!lastSave || (now - lastSave) >= 30000) {
                await this.saveUser(user);
                this.lastUserSaveMiddleware.set(userId, now);
            }
            
            // Check if user is member of required channel (if set)
            if (this.requiredChannelId && !this.adminIds.includes(user.id)) {
                try {
                    const member = await ctx.telegram.getChatMember(this.requiredChannelId, user.id);
                    if (!['member', 'administrator', 'creator'].includes(member.status)) {
                        await ctx.reply('❌ You must join our channel first to use this bot.');
                        return;
                    }
                } catch (error) {
                    logger.warn(`Could not check channel membership for user ${user.id}: ${error.message}`);
                }
            }
            
            return next();
        });
    }

    /**
     * Set up command handlers
     */
    setupCommands() {
        // Start command
        this.bot.command('start', async (ctx) => {
            try {
                // Ensure user exists in database first
                await this.ensureUserExists(ctx);

                await this.updateUserStats(ctx.from.id, { lastActivity: new Date() });

                const welcomeMessage = this.getMessage('welcome.start');
                await ctx.reply(welcomeMessage);

                // Show main menu after welcome message
                await this.showMainMenu(ctx);

                logger.info(`User ${ctx.from.id} started the bot`);
            } catch (error) {
                logger.error('Error in start command:', error);
                await ctx.reply(this.getMessage('error.general'));
            }
        });

        // Help command
        this.bot.command('help', async (ctx) => {
            const helpMessage = this.getMessage('welcome.help');
            await ctx.reply(helpMessage);
        });

        // Stats command
        this.bot.command('stats', async (ctx) => {
            const userStats = await this.getUserStats(ctx.from.id);
            const statsMessage = `
📊 **Your Statistics**

📝 Questions Generated: ${userStats.questionsGenerated || 0}
📁 Files Processed: ${userStats.filesProcessed || 0}
📅 Member Since: ${userStats.joinedAt ? new Date(userStats.joinedAt).toLocaleDateString() : 'Unknown'}
⏰ Last Activity: ${userStats.lastActivity ? new Date(userStats.lastActivity).toLocaleString() : 'Now'}

🎯 Keep learning with X-Stein!
            `;
            
            await ctx.replyWithMarkdown(statsMessage);
        });

        // Admin commands
        this.bot.command('admin', async (ctx) => {
            if (!this.adminIds.includes(ctx.from.id)) {
                await ctx.reply('❌ You are not authorized to use admin commands.');
                return;
            }
            
            const adminMessage = `
🔧 **Admin Panel**

**Bot Statistics:**
👥 Total Users: ${this.stats.totalUsers}
🟢 Active Users: ${this.stats.activeUsers}
📝 Questions Generated: ${this.stats.questionsGenerated}
📁 Files Processed: ${this.stats.filesProcessed}
⏰ Uptime: ${this.getUptime()}

**Commands:**
/broadcast - Send message to all users
/users - View user list
/ban - Ban a user
/unban - Unban a user

Use the admin control panel for more options!
            `;
            
            await ctx.replyWithMarkdown(adminMessage);
        });
    }

    /**
     * Set up message handlers for file processing
     */
    setupMessageHandlers() {
        // Handle document uploads
        this.bot.on('document', async (ctx) => {
            const userId = ctx.from.id;
            const selectedQuestionType = this.userQuestionTypes.get(userId);

            if (!selectedQuestionType) {
                // User hasn't selected question type, show main menu
                await ctx.reply(this.getMessage('questionTypeSelection.mustSelectFirst'));
                await this.showMainMenu(ctx);
                return;
            }

            await this.handleFileUpload(ctx, ctx.message.document);
        });

        // Handle photo uploads
        this.bot.on('photo', async (ctx) => {
            const userId = ctx.from.id;
            const selectedQuestionType = this.userQuestionTypes.get(userId);

            if (!selectedQuestionType) {
                // User hasn't selected question type, show main menu
                await ctx.reply(this.getMessage('questionTypeSelection.mustSelectFirst'));
                await this.showMainMenu(ctx);
                return;
            }

            const photo = ctx.message.photo[ctx.message.photo.length - 1]; // Get highest resolution
            await this.handleFileUpload(ctx, photo);
        });

        // Handle text messages
        this.bot.on('text', async (ctx) => {
            const text = ctx.message.text;

            // Skip if it's a command
            if (text.startsWith('/')) return;

            // Custom count input removed - now using automatic count based on user type
            const userId = ctx.from.id;

            // Handle direct text input for question generation
            if (text.length > 50) {
                const userId = ctx.from.id;
                const selectedQuestionType = this.userQuestionTypes.get(userId);

                if (!selectedQuestionType) {
                    // User hasn't selected question type, show main menu
                    await ctx.reply(this.getMessage('questionTypeSelection.mustSelectFirst'));
                    await this.showMainMenu(ctx);
                    return;
                }

                await this.handleTextInput(ctx, text);
            } else {
                // Show main menu for any other text message
                await this.showMainMenu(ctx);
            }
        });
    }

    /**
     * Set up callback query handlers for inline keyboards
     */
    setupCallbackHandlers() {


        // Count selection removed - now using automatic count based on user type

        // Quiz system callback handlers
        this.bot.action(/^start_quiz:(.+)$/, async (ctx) => {
            const [, sessionId] = ctx.match;
            await ctx.answerCbQuery(this.getMessage('quiz.starting'));
            await this.startQuiz(ctx, sessionId);
        });

        this.bot.action(/^show_answers:(.+)$/, async (ctx) => {
            const [, sessionId] = ctx.match;
            await ctx.answerCbQuery(this.getMessage('quiz.reviewAnswers'));
            const session = this.userSessions.get(sessionId);
            if (session && session.questions) {
                await this.sendQuestions(ctx, session.questions, session);
            } else {
                await ctx.reply(this.getMessage('error.sessionNotFound'));
            }
        });

        // Download PDF handler
        this.bot.action(/^download_pdf:(.+)$/, async (ctx) => {
            const [, sessionId] = ctx.match;
            await ctx.answerCbQuery('📄 جاري إنشاء ملف PDF...');
            const session = this.userSessions.get(sessionId);
            if (session && session.questions) {
                await this.generateAndSendPDF(ctx, session);
            } else {
                await ctx.reply(this.getMessage('error.sessionNotFound'));
            }
        });

        // Save quiz handler
        this.bot.action(/^save_quiz:(.+)$/, async (ctx) => {
            const [, sessionId] = ctx.match;
            await ctx.answerCbQuery('💾 جاري حفظ الاختبار...');
            const session = this.userSessions.get(sessionId);
            if (session && session.questions) {
                await this.saveQuizForUser(ctx, session);
            } else {
                await ctx.reply(this.getMessage('error.sessionNotFound'));
            }
        });

        this.bot.action(/^quiz_answer:(.+):(.+)$/, async (ctx) => {
            const [, sessionId, answer] = ctx.match;
            await ctx.answerCbQuery(`Selected: ${answer}`);
            await this.handleQuizAnswer(ctx, sessionId, answer);
        });



        // Next question handler
        this.bot.action(/^next_question:(.+)$/, async (ctx) => {
            const [, sessionId] = ctx.match;
            await ctx.answerCbQuery('➡️ جاري تحميل السؤال التالي...');
            const session = this.userSessions.get(sessionId);
            if (session && session.quizState) {
                await this.showQuizQuestion(ctx, session);
            } else {
                await ctx.reply(this.getMessage('error.quizNotFound'));
            }
        });

        // Show final results handler
        this.bot.action(/^show_final_results:(.+)$/, async (ctx) => {
            const [, sessionId] = ctx.match;
            await ctx.answerCbQuery('📊 جاري عرض النتائج النهائية...');
            const session = this.userSessions.get(sessionId);
            if (session && session.quizState) {
                // Delete the quiz message when showing final results
                try {
                    if (session.quizState.messageId) {
                        await ctx.telegram.deleteMessage(ctx.chat.id, session.quizState.messageId);
                    }
                } catch (error) {
                    logger.debug('Could not delete quiz message:', error.message);
                }

                await this.showQuizResults(ctx, session);
            } else {
                await ctx.reply(this.getMessage('error.quizNotFound'));
            }
        });

        // Main Menu Handlers
        this.bot.action('main_menu', async (ctx) => {
            await ctx.answerCbQuery();
            await this.showMainMenu(ctx, true); // true = edit message
        });

        this.bot.action('saved_quizzes', async (ctx) => {
            await ctx.answerCbQuery();
            await this.showSavedQuizzes(ctx, true); // true = edit message
        });

        this.bot.action('how_it_works', async (ctx) => {
            await ctx.answerCbQuery();
            await this.showHowItWorks(ctx, true); // true = edit message
        });

        // Question Type Selection Handlers
        this.bot.action(/^select_question_type:(.+)$/, async (ctx) => {
            const [, questionType] = ctx.match;
            await ctx.answerCbQuery();
            await this.handleQuestionTypeSelection(ctx, questionType);
        });

        // Saved Quiz Handlers
        this.bot.action(/^load_saved_quiz:(.+)$/, async (ctx) => {
            const [, quizId] = ctx.match;
            await ctx.answerCbQuery('📂 جاري تحميل الاختبار...');
            await this.loadSavedQuiz(ctx, quizId, true); // true = edit message
        });

        this.bot.action(/^delete_saved_quiz:(.+)$/, async (ctx) => {
            const [, quizId] = ctx.match;
            await ctx.answerCbQuery('🗑️ جاري حذف الاختبار...');
            await this.deleteSavedQuiz(ctx, quizId, true); // true = edit message
        });
    }

    /**
     * Handle file uploads from users
     */
    async handleFileUpload(ctx, file) {
        try {
            const userId = ctx.from.id;

            // Ensure user exists in database
            await this.ensureUserExists(ctx);

            const selectedQuestionType = this.userQuestionTypes.get(userId);

            // Get user type for rate limiting
            const userType = await this.getUserType(userId);

            // Check file processing limits
            const fileCheck = rateLimitService.canProcessFile(userId, userType);
            if (!fileCheck.allowed) {
                const message = this.getMessage('error.dailyLimitReached')
                    .replace('{current}', fileCheck.current)
                    .replace('{limit}', fileCheck.limit);
                await ctx.reply(message);
                return;
            }

            // Check cooldown period
            const cooldownCheck = rateLimitService.canGenerateQuestions(userId, userType);
            if (!cooldownCheck.allowed) {
                const message = this.getMessage('error.cooldownActive')
                    .replace('{minutes}', cooldownCheck.remainingCooldown);
                await ctx.reply(message);
                return;
            }

            const processingMsg = await ctx.reply(this.getMessage('file.processing'));

            // Download file from Telegram
            const fileLink = await ctx.telegram.getFileLink(file.file_id);
            const filePath = await this.downloadTelegramFile(fileLink.href, file);

            // Create user session with pre-selected question type
            const sessionId = `${ctx.from.id}_${Date.now()}`;
            const session = {
                userId: ctx.from.id,
                filePath: filePath,
                fileName: file.file_name || 'uploaded_file',
                fileType: this.getFileType(file),
                content: null,
                questionType: selectedQuestionType, // Use pre-selected type
                questionCount: null,
                processingMsg: processingMsg
            };

            this.userSessions.set(sessionId, session);

            // Use your existing desktop app's file processing through IPC
            // This will use your Python extraction service and all optimizations
            const extractionResult = await this.processFileWithDesktopApp(filePath);

            if (!extractionResult || !extractionResult.success || !extractionResult.text) {
                await ctx.telegram.editMessageText(
                    ctx.chat.id,
                    processingMsg.message_id,
                    null,
                    this.getMessage('file.error', { error: extractionResult?.error || 'No text could be extracted from the file' })
                );
                return;
            }
            
            session.content = extractionResult.text;
            session.extractionResult = extractionResult; // Store full result for later use

            // Get file type description
            const fileTypeDescriptions = {
                'image': '🖼️ Image (OCR processed)',
                'pdf': '📄 PDF Document',
                'document': '📝 Word Document',
                'spreadsheet': '📊 Excel Spreadsheet',
                'presentation': '📽️ PowerPoint Presentation',
                'text': '📄 Text File',
                'unknown': '📎 File'
            };

            const fileTypeDesc = fileTypeDescriptions[session.fileType] || '📎 File';
            const pageInfo = extractionResult.pageCount ? ` (${extractionResult.pageCount} pages)` : '';

            // Get question count based on user type, file type, and rate limits
            const isImage = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(session.fileType);
            const pageCount = extractionResult.pageCount || 1;
            const questionCount = rateLimitService.getQuestionCount(userType, isImage, pageCount);
            session.questionCount = questionCount;

            const cleanFileName = this.cleanText(session.fileName);
            const questionTypeText = session.questionType === 'mcq' ? 'الاختيار من متعدد' : 'صح/خطأ';

            // Show different message based on file type
            let questionCountText;
            if (isImage) {
                questionCountText = `🔢 عدد الأسئلة: ${questionCount} (للصورة)`;
            } else {
                const limits = rateLimitService.getLimits(userType);
                const questionsPerPage = limits.questionsPerPage || 5;
                questionCountText = `🔢 عدد الأسئلة: ${questionCount} (${questionsPerPage} لكل صفحة)`;
            }

            const message = `✅ تم معالجة الملف بنجاح!\n\n📄 ${fileTypeDesc}: ${cleanFileName}${pageInfo}\n📊 ${session.content.length} حرف\n🎯 نوع الأسئلة: ${questionTypeText}\n${questionCountText}\n\n⏳ جاري إنشاء الأسئلة...`;

            await ctx.telegram.editMessageText(
                ctx.chat.id,
                processingMsg.message_id,
                null,
                message
            );

            // Start question generation immediately
            await this.generateQuestions(ctx, session);
            
        } catch (error) {
            logger.error('Error handling file upload:', error);
            await ctx.reply(this.getMessage('file.error', { error: error.message }));
        }
    }

    /**
     * Handle direct text input for question generation
     */
    async handleTextInput(ctx, text) {
        try {
            const userId = ctx.from.id;
            const selectedQuestionType = this.userQuestionTypes.get(userId);

            const sessionId = `${ctx.from.id}_${Date.now()}`;
            const session = {
                userId: ctx.from.id,
                content: text,
                fileName: 'Direct Text Input',
                fileType: 'text',
                questionType: selectedQuestionType, // Use pre-selected type
                questionCount: null,
                extractionResult: {
                    text: text,
                    success: true,
                    contentType: 'text',
                    isScanned: false
                }
            };

            this.userSessions.set(sessionId, session);

            // Get user type and question count from rate limits (text input is treated as document)
            const userType = await this.getUserType(userId);
            const questionCount = rateLimitService.getQuestionCount(userType, false, 1); // false = not image, 1 page
            session.questionCount = questionCount;

            const questionTypeText = selectedQuestionType === 'mcq' ? 'الاختيار من متعدد' : 'صح/خطأ';
            const message = `✅ تم استلام النص!\n\n📊 ${text.length} حرف\n🎯 نوع الأسئلة: ${questionTypeText}\n🔢 عدد الأسئلة: ${questionCount}\n\n⏳ جاري إنشاء الأسئلة...`;

            const loadingMsg = await ctx.reply(message);
            session.generationMessageId = loadingMsg.message_id;

            // Start question generation immediately
            await this.generateQuestions(ctx, session);

        } catch (error) {
            logger.error('Error handling text input:', error);
            await ctx.reply(this.getMessage('error.processingFailed'));
        }
    }

    /**
     * Generate questions using the API service
     */
    async generateQuestions(ctx, session) {
        try {
            // Use existing message if available, otherwise create new one
            let loadingMsg;
            if (session.generationMessageId) {
                loadingMsg = { message_id: session.generationMessageId };
            } else {
                loadingMsg = await ctx.reply(this.getMessage('quiz.generating'));
            }

            // Use your desktop app's AI system through IPC
            // This will use Gemini CLI as primary and OpenRouter as fallback
            const questions = await this.generateQuestionsWithDesktopApp(
                session.content,
                session.questionType,
                session.questionCount,
                session.extractionResult
            );

            if (!questions || questions.length === 0) {
                await ctx.telegram.editMessageText(
                    ctx.chat.id,
                    loadingMsg.message_id,
                    null,
                    this.getMessage('error.generationFailed')
                );
                return;
            }

            // Update statistics
            logger.debug(`Updating user stats for user ${session.userId}:`, {
                questionsGenerated: questions.length,
                filesProcessed: 1
            });
            await this.updateUserStats(session.userId, {
                questionsGenerated: questions.length,
                filesProcessed: 1
            });

            // Record rate limiting events
            rateLimitService.recordFileProcessing(session.userId);
            rateLimitService.recordQuestionGeneration(session.userId);

            // Store questions in session for quiz or answers
            session.questions = questions;
            session.sessionId = `${session.userId}_${Date.now()}`;
            this.userSessions.set(session.sessionId, session);

            // Edit the loading message to show options instead of deleting it
            await this.showQuizOrAnswersOptions(ctx, session, loadingMsg.message_id);

        } catch (error) {
            logger.error('Error generating questions:', error);
            await ctx.reply(this.getMessage('error.generationFailed'));
        }
    }

    /**
     * Send formatted questions to user
     */
    async sendQuestions(ctx, questions, session) {
        try {
            const questionType = session.questionType === 'mcq' ? this.getMessage('buttons.mcq') : this.getMessage('buttons.truefalse');
            const escapedFileName = this.escapeMarkdown(session.fileName);
            const header = this.getMessage('quiz.generated', {
                count: questions.length,
                type: questionType,
                fileName: escapedFileName
            }) + '\n\n';

            let messageText = header;

            for (let i = 0; i < questions.length; i++) {
                const q = questions[i];
                const questionNum = i + 1;

                if (session.questionType === 'mcq') {
                    messageText += `${questionNum}. ${this.cleanText(q.question)}\n`;

                    // Handle both array and object formats for options
                    let optionA, optionB, optionC, optionD;
                    if (Array.isArray(q.options)) {
                        optionA = this.cleanText(q.options[0] || 'Option A');
                        optionB = this.cleanText(q.options[1] || 'Option B');
                        optionC = this.cleanText(q.options[2] || 'Option C');
                        optionD = this.cleanText(q.options[3] || 'Option D');
                    } else if (q.options && typeof q.options === 'object') {
                        optionA = this.cleanText(q.options.A || q.options['A'] || 'Option A');
                        optionB = this.cleanText(q.options.B || q.options['B'] || 'Option B');
                        optionC = this.cleanText(q.options.C || q.options['C'] || 'Option C');
                        optionD = this.cleanText(q.options.D || q.options['D'] || 'Option D');
                    } else {
                        optionA = 'Option A';
                        optionB = 'Option B';
                        optionC = 'Option C';
                        optionD = 'Option D';
                    }

                    messageText += `A) ${optionA}\n`;
                    messageText += `B) ${optionB}\n`;
                    messageText += `C) ${optionC}\n`;
                    messageText += `D) ${optionD}\n\n`;
                    messageText += `✅ Answer: ${this.cleanText(q.answer)}\n\n`;
                    if (q.explanation) {
                        messageText += `💡 Explanation: ${this.cleanText(q.explanation)}\n\n`;
                    }
                } else {
                    messageText += `${questionNum}. ${this.cleanText(q.question)}\n`;

                    // Handle different answer formats for True/False
                    let displayAnswer;
                    if (typeof q.answer === 'string') {
                        displayAnswer = q.answer; // "True" or "False"
                    } else if (typeof q.answer === 'boolean') {
                        displayAnswer = q.answer ? 'True' : 'False';
                    } else {
                        displayAnswer = 'True'; // Fallback
                    }

                    messageText += `✅ Answer: ${this.cleanText(displayAnswer)}\n\n`;
                    if (q.explanation) {
                        messageText += `💡 Explanation: ${this.cleanText(q.explanation)}\n\n`;
                    }
                }

                messageText += '\n';

                // Split long messages
                if (messageText.length > 3500) {
                    await ctx.reply(messageText);
                    messageText = '';
                }
            }

            // Send remaining content
            if (messageText.length > 0) {
                await ctx.reply(messageText);
            }

            // Send completion message with options
            const keyboard = Markup.inlineKeyboard([
                [Markup.button.callback('📊 إنشاء المزيد', 'generate_more')],
                [Markup.button.callback('📈 إحصائياتي', 'my_stats')]
            ]);

            await ctx.reply(this.getMessage('success.questionsGenerated'), keyboard);

        } catch (error) {
            logger.error('Error sending questions:', error);
            await ctx.reply(this.getMessage('error.processingFailed'));
        }
    }

    /**
     * Download file from Telegram servers
     */
    async downloadTelegramFile(fileUrl, fileInfo) {
        try {
            const response = await fetch(fileUrl);
            const buffer = await response.arrayBuffer();

            // Create temp directory if it doesn't exist
            const tempDir = path.join(__dirname, '../../temp');
            if (!fs.existsSync(tempDir)) {
                fs.mkdirSync(tempDir, { recursive: true });
            }

            // Generate unique filename
            const timestamp = Date.now();
            const extension = this.getFileExtension(fileInfo);
            const fileName = `telegram_${timestamp}${extension}`;
            const filePath = path.join(tempDir, fileName);

            // Write file to disk
            fs.writeFileSync(filePath, Buffer.from(buffer));

            logger.info(`Downloaded Telegram file: ${fileName}`);
            return filePath;

        } catch (error) {
            logger.error('Error downloading Telegram file:', error);
            throw error;
        }
    }

    /**
     * Get file extension from Telegram file info
     */
    getFileExtension(fileInfo) {
        if (fileInfo.file_name) {
            return path.extname(fileInfo.file_name);
        }

        // For photos, default to .jpg
        if (fileInfo.width && fileInfo.height) {
            return '.jpg';
        }

        return '.bin';
    }

    /**
     * Process file using your desktop app's file processing system
     * This delegates to your existing Python extraction service and optimizations
     */
    async processFileWithDesktopApp(filePath) {
        try {
            // Use your existing file service directly
            const fileService = require('./fileService');

            const result = await fileService.extractTextFromDocument(filePath, {
                imageQuestions: 15, // Use your default settings
                pageQuestions: 5
            });

            // The fileService returns { text, pageCount, questionCount, ... }
            // We need to add success flag for the Telegram bot
            if (result && result.text && result.text.trim().length > 0) {
                return {
                    success: true,
                    text: result.text,
                    pageCount: result.pageCount,
                    questionCount: result.questionCount,
                    confidence: result.confidence,
                    method: result.method
                };
            } else {
                return {
                    success: false,
                    error: 'No text could be extracted from the file'
                };
            }
        } catch (error) {
            logger.error('Error processing file with desktop app:', error);
            return {
                success: false,
                error: error.message || 'Failed to process file'
            };
        }
    }

    /**
     * Generate questions using your desktop app's AI system
     * This uses Gemini CLI as primary with Google account switching, then OpenRouter as fallback
     */
    async generateQuestionsWithDesktopApp(content, questionType, questionCount, extractionResult) {
        try {
            // First try your Gemini CLI system with account switching
            const geminiResult = await this.tryGeminiCLIGeneration(content, questionType, questionCount);

            if (geminiResult.success && geminiResult.questions) {
                logger.success('Used AI Agent CLI successfully');
                return geminiResult.questions;
            }

            // If AI Agent CLI failed, fall back to OpenRouter
            logger.info('AI Agent CLI failed, falling back to OpenRouter...');
            const apiService = require('./apiService');

            // Map question type to the format expected by your AI system
            const aiQuestionType = questionType === 'mcq' ? 'MCQ' : 'TF';
            logger.debug(`Using '${aiQuestionType}' for OpenRouter fallback`);

            const questions = await apiService.generateQuestionsFromAPI(
                content,
                aiQuestionType,
                questionCount,
                2, // retries
                extractionResult?.isScanned || false,
                null, // userId for Telegram
                extractionResult?.contentType || 'text',
                'auto' // Use OpenRouter model selection
            );

            logger.success('Used OpenRouter fallback successfully');
            return questions;
        } catch (error) {
            logger.error('Error generating questions with desktop app:', error);
            throw error;
        }
    }

    /**
     * Try to generate questions using your Gemini CLI system with account switching
     */
    async tryGeminiCLIGeneration(content, questionType, questionCount) {
        try {
            if (!this.ipcHandlers || !this.ipcHandlers.callGeminiCLIWithFailover) {
                logger.warn('AI Agent CLI system not available, skipping to OpenRouter');
                return { success: false, error: 'AI Agent CLI system not available' };
            }

            logger.info('Trying AI Agent CLI with account switching...');

            // Map question type to the format expected by your AI system
            const aiQuestionType = questionType === 'mcq' ? 'MCQ' : 'TF';

            logger.debug(`Converting '${questionType}' to '${aiQuestionType}' for AI system`);

            // Use your existing Gemini CLI system with account switching
            const result = await this.ipcHandlers.callGeminiCLIWithFailover(content, {
                type: aiQuestionType,
                count: questionCount
            }, 'questions');

            return result;
        } catch (error) {
            logger.error('Error trying Gemini CLI generation:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Show quiz or answers options after questions are generated
     */
    async showQuizOrAnswersOptions(ctx, session, messageId = null) {
        try {
            const questionType = session.questionType === 'mcq' ? this.getMessage('buttons.mcq') : this.getMessage('buttons.truefalse');
            const keyboard = Markup.inlineKeyboard([
                [Markup.button.callback(this.getMessage('buttons.takeQuiz'), `start_quiz:${session.sessionId}`)],
                [Markup.button.callback(this.getMessage('buttons.showAnswers'), `show_answers:${session.sessionId}`)],
                [Markup.button.callback(this.getMessage('buttons.saveQuiz'), `save_quiz:${session.sessionId}`)],
                [Markup.button.callback(this.getMessage('buttons.mainMenu'), 'main_menu')]
            ]);

            const message = this.getMessage('quiz.generated', {
                count: session.questions.length,
                type: questionType,
                fileName: this.cleanText(session.fileName)
            });

            if (messageId) {
                // Edit existing message
                await ctx.telegram.editMessageText(
                    ctx.chat.id,
                    messageId,
                    null,
                    message,
                    { reply_markup: keyboard.reply_markup }
                );
                session.optionsMessageId = messageId;
            } else {
                // Create new message
                const optionsMessage = await ctx.reply(message, {
                    reply_markup: keyboard.reply_markup
                });
                session.optionsMessageId = optionsMessage.message_id;
            }
        } catch (error) {
            logger.error('Error showing quiz/answers options:', error);
            await ctx.reply(this.getMessage('error.processingFailed'));
        }
    }

    /**
     * Start interactive quiz
     */
    async startQuiz(ctx, sessionId) {
        try {
            const session = this.userSessions.get(sessionId);
            if (!session || !session.questions) {
                await ctx.reply(this.getMessage('error.quizNotFound'));
                return;
            }

            // Initialize quiz state
            session.quizState = {
                currentQuestion: 0,
                score: 0,
                answers: [],
                startTime: Date.now(),
                messageId: null
            };

            // Delete the options message to keep chat clean
            try {
                if (session.optionsMessageId) {
                    await ctx.telegram.deleteMessage(ctx.chat.id, session.optionsMessageId);
                }
            } catch (error) {
                logger.debug('Could not delete options message:', error.message);
            }

            // Show first question directly (no starting message)
            await this.showQuizQuestion(ctx, session);

        } catch (error) {
            logger.error('Error starting quiz:', error);
            await ctx.reply(this.getMessage('error.processingFailed'));
        }
    }

    /**
     * Show current quiz question
     */
    async showQuizQuestion(ctx, session) {
        try {
            const { currentQuestion } = session.quizState;
            const question = session.questions[currentQuestion];
            const questionNum = currentQuestion + 1;
            const totalQuestions = session.questions.length;

            let keyboard;
            let questionText = this.getMessage('quiz.questionHeader', {
                current: questionNum,
                total: totalQuestions,
                question: this.cleanText(question.question)
            });

            if (session.questionType === 'mcq') {
                // Handle both array and object formats for options
                let optionA, optionB, optionC, optionD;

                if (Array.isArray(question.options)) {
                    optionA = this.cleanText(question.options[0]?.text || question.options[0] || 'Option A');
                    optionB = this.cleanText(question.options[1]?.text || question.options[1] || 'Option B');
                    optionC = this.cleanText(question.options[2]?.text || question.options[2] || 'Option C');
                    optionD = this.cleanText(question.options[3]?.text || question.options[3] || 'Option D');
                } else if (question.options && typeof question.options === 'object') {
                    optionA = this.cleanText(question.options.A || question.options['A'] || question.options.a || 'Option A');
                    optionB = this.cleanText(question.options.B || question.options['B'] || question.options.b || 'Option B');
                    optionC = this.cleanText(question.options.C || question.options['C'] || question.options.c || 'Option C');
                    optionD = this.cleanText(question.options.D || question.options['D'] || question.options.d || 'Option D');
                } else if (question.optionA || question.option_a) {
                    optionA = this.cleanText(question.optionA || question.option_a || 'Option A');
                    optionB = this.cleanText(question.optionB || question.option_b || 'Option B');
                    optionC = this.cleanText(question.optionC || question.option_c || 'Option C');
                    optionD = this.cleanText(question.optionD || question.option_d || 'Option D');
                } else {
                    const questionTextForParsing = question.question || '';
                    const optionMatches = questionTextForParsing.match(/[A-D]\)\s*([^\n]+)/g);
                    if (optionMatches && optionMatches.length >= 4) {
                        optionA = this.cleanText(optionMatches[0].replace(/^A\)\s*/, ''));
                        optionB = this.cleanText(optionMatches[1].replace(/^B\)\s*/, ''));
                        optionC = this.cleanText(optionMatches[2].replace(/^C\)\s*/, ''));
                        optionD = this.cleanText(optionMatches[3].replace(/^D\)\s*/, ''));
                    } else {
                        optionA = 'Option A';
                        optionB = 'Option B';
                        optionC = 'Option C';
                        optionD = 'Option D';
                    }
                }

                questionText += `A) ${optionA}\n\n`;
                questionText += `B) ${optionB}\n\n`;
                questionText += `C) ${optionC}\n\n`;
                questionText += `D) ${optionD}`;

                keyboard = Markup.inlineKeyboard([
                    [
                        Markup.button.callback(this.getMessage('buttons.optionA'), `quiz_answer:${session.sessionId}:A`),
                        Markup.button.callback(this.getMessage('buttons.optionB'), `quiz_answer:${session.sessionId}:B`)
                    ],
                    [
                        Markup.button.callback(this.getMessage('buttons.optionC'), `quiz_answer:${session.sessionId}:C`),
                        Markup.button.callback(this.getMessage('buttons.optionD'), `quiz_answer:${session.sessionId}:D`)
                    ]
                ]);
            } else {
                keyboard = Markup.inlineKeyboard([
                    [
                        Markup.button.callback(this.getMessage('buttons.optionTrue'), `quiz_answer:${session.sessionId}:true`),
                        Markup.button.callback(this.getMessage('buttons.optionFalse'), `quiz_answer:${session.sessionId}:false`)
                    ]
                ]);
            }

            // Store or update the quiz message
            if (session.quizState.messageId) {
                // Edit existing message
                await ctx.telegram.editMessageText(
                    ctx.chat.id,
                    session.quizState.messageId,
                    null,
                    questionText,
                    { reply_markup: keyboard.reply_markup }
                );
            } else {
                // Send new message and store message ID
                const sentMessage = await ctx.reply(questionText, {
                    reply_markup: keyboard.reply_markup
                });
                session.quizState.messageId = sentMessage.message_id;
            }

        } catch (error) {
            logger.error('Error showing quiz question:', error);
            await ctx.reply(this.getMessage('error.processingFailed'));
        }
    }

    /**
     * Handle quiz answer
     */
    async handleQuizAnswer(ctx, sessionId, answer) {
        try {
            const session = this.userSessions.get(sessionId);
            if (!session || !session.quizState) {
                await ctx.reply(this.getMessage('error.quizNotFound'));
                return;
            }

            const { currentQuestion } = session.quizState;
            const question = session.questions[currentQuestion];
            let isCorrect = false;

            // Clean, colorful quiz logging (only for important events)
            if (session.quizState.currentQuestion === 0 || session.quizState.currentQuestion === session.questions.length - 1) {
                const chalk = require('chalk');
                const questionNum = session.quizState.currentQuestion + 1;
                const total = session.questions.length;
                const isFirst = questionNum === 1;
                const isLast = questionNum === total;

                if (isFirst) {
                    logger.info(`Quiz started - Q${questionNum}/${total} - User: ${answer}`);
                } else if (isLast) {
                    logger.success(`Quiz completed - Q${questionNum}/${total} - User: ${answer}`);
                }
            }

            // Check if answer is correct
            if (session.questionType === 'mcq') {
                // MCQ questions use 'answer' field, not 'correct_answer'
                isCorrect = answer === question.answer;
                // MCQ answer comparison (logging removed for cleaner output)
            } else {
                // For True/False questions
                const userAnswer = answer === 'true';
                let correctAnswer = question.answer || question.correct_answer;

                // TF answer processing (reduced logging)

                // Handle different answer formats
                if (typeof correctAnswer === 'string') {
                    const lowerAnswer = correctAnswer.toLowerCase();
                    if (lowerAnswer === 'true' || lowerAnswer === 'صحيح') {
                        correctAnswer = true;
                    } else if (lowerAnswer === 'false' || lowerAnswer === 'خطأ') {
                        correctAnswer = false;
                    } else {
                        // Try to determine from explanation
                        if (question.explanation) {
                            const explanation = question.explanation.toLowerCase();
                            if (explanation.includes('this is false') || explanation.includes('false.') || explanation.includes('خطأ')) {
                                correctAnswer = false;
                            } else if (explanation.includes('this is true') || explanation.includes('true.') || explanation.includes('صحيح')) {
                                correctAnswer = true;
                            } else {
                                correctAnswer = false; // Default to false if unclear
                            }
                        } else {
                            correctAnswer = false; // Default fallback
                        }
                    }
                } else if (typeof correctAnswer === 'boolean') {
                    // Already boolean, use as is
                } else {
                    // Check explanation to determine correct answer
                    if (question.explanation) {
                        const explanation = question.explanation.toLowerCase();
                        if (explanation.includes('this is false') || explanation.includes('false.') || explanation.includes('خطأ')) {
                            correctAnswer = false;
                        } else if (explanation.includes('this is true') || explanation.includes('true.') || explanation.includes('صحيح')) {
                            correctAnswer = true;
                        } else {
                            correctAnswer = false; // Default to false if unclear
                        }
                    } else {
                        correctAnswer = false; // Default fallback
                    }
                }

                // Answer processed (logging reduced)
                isCorrect = userAnswer === correctAnswer;
            }

            // Get the correct answer for display (logging removed to reduce spam)
            let displayCorrectAnswer;

            if (session.questionType === 'mcq') {
                // MCQ questions use 'answer' field
                displayCorrectAnswer = question.answer || question.correct_answer;

                // If still undefined, try to get from options
                if (!displayCorrectAnswer && question.options) {
                    // Find the correct option
                    const correctOption = question.options.find(opt => opt.correct);
                    if (correctOption) {
                        displayCorrectAnswer = correctOption.text;
                    }
                }

                // Final fallback
                if (!displayCorrectAnswer) {
                    displayCorrectAnswer = 'A'; // Default fallback
                }

                // MCQ answer determined
            } else {
                // For True/False questions - fix the logic
                let correctAnswer = question.answer || question.correct_answer;

                // Processing TF answer

                if (typeof correctAnswer === 'string') {
                    // Handle string values
                    const lowerAnswer = correctAnswer.toLowerCase();
                    if (lowerAnswer === 'true' || lowerAnswer === 'صحيح') {
                        displayCorrectAnswer = 'True';
                    } else if (lowerAnswer === 'false' || lowerAnswer === 'خطأ') {
                        displayCorrectAnswer = 'False';
                    } else {
                        displayCorrectAnswer = correctAnswer; // Use as-is if unclear
                    }
                } else if (typeof correctAnswer === 'boolean') {
                    displayCorrectAnswer = correctAnswer ? 'True' : 'False';
                } else {
                    // Check explanation to determine correct answer
                    if (question.explanation) {
                        const explanation = question.explanation.toLowerCase();
                        if (explanation.includes('this is false') || explanation.includes('false.') || explanation.includes('خطأ')) {
                            displayCorrectAnswer = 'False';
                        } else if (explanation.includes('this is true') || explanation.includes('true.') || explanation.includes('صحيح')) {
                            displayCorrectAnswer = 'True';
                        } else {
                            displayCorrectAnswer = 'False'; // Default to False if unclear
                        }
                    } else {
                        displayCorrectAnswer = 'False'; // Default fallback
                    }
                }

                // TF answer determined
            }

            // Store answer
            session.quizState.answers.push({
                question: currentQuestion,
                userAnswer: answer,
                correct: isCorrect,
                correctAnswer: displayCorrectAnswer
            });

            if (isCorrect) {
                session.quizState.score++;
            }

            // Show result for this question
            let explanationText = '';

            if (isCorrect) {
                if (session.questionType === 'tf') {
                    // For True/False, include the question and confirmation
                    explanationText = `✅ صحيح!\n\n📝 السؤال: ${this.cleanText(question.question)}\n\n✅ الإجابة الصحيحة: ${displayCorrectAnswer}`;
                } else {
                    explanationText = this.getMessage('quiz.correctAnswer');
                }
            } else {
                if (session.questionType === 'tf') {
                    // For True/False incorrect answers, also include the question
                    explanationText = `❌ خطأ!\n\n📝 السؤال: ${this.cleanText(question.question)}\n\n✅ الإجابة الصحيحة: ${displayCorrectAnswer}`;
                } else {
                    explanationText = this.getMessage('quiz.incorrectAnswer', { answer: displayCorrectAnswer });
                }
            }

            if (question.explanation) {
                explanationText += '\n\n' + this.getMessage('quiz.explanation', { explanation: this.cleanText(question.explanation) });
            }

            // Move to next question or show final results
            session.quizState.currentQuestion++;

            let keyboard;
            if (session.quizState.currentQuestion < session.questions.length) {
                // Add "Next Question" button
                keyboard = Markup.inlineKeyboard([
                    [Markup.button.callback(this.getMessage('buttons.nextQuestion'), `next_question:${session.sessionId}`)]
                ]);
            } else {
                // Quiz completed, add "Show Results" button
                keyboard = Markup.inlineKeyboard([
                    [Markup.button.callback(this.getMessage('buttons.showFinalResults'), `show_final_results:${session.sessionId}`)]
                ]);
            }

            // Edit the same quiz message with answer result and next action button
            await ctx.telegram.editMessageText(
                ctx.chat.id,
                session.quizState.messageId,
                null,
                explanationText,
                { reply_markup: keyboard.reply_markup }
            );

        } catch (error) {
            logger.error('Error handling quiz answer:', error);
            await ctx.reply(this.getMessage('error.processingFailed'));
        }
    }

    /**
     * Show final quiz results
     */
    async showQuizResults(ctx, session) {
        try {
            const { score, answers, startTime } = session.quizState;
            const totalQuestions = session.questions.length;
            const percentage = Math.round((score / totalQuestions) * 100);
            const timeTaken = Math.round((Date.now() - startTime) / 1000);

            let resultEmoji = '🎉';
            let resultMessage = this.getMessage('results.excellent');

            if (percentage >= 80) {
                resultEmoji = '🎉';
                resultMessage = this.getMessage('results.excellent');
            } else if (percentage >= 60) {
                resultEmoji = '👏';
                resultMessage = this.getMessage('results.good');
            } else if (percentage >= 40) {
                resultEmoji = '👍';
                resultMessage = this.getMessage('results.notBad');
            } else {
                resultEmoji = '📚';
                resultMessage = this.getMessage('results.keepStudying');
            }

            const finalMessage = this.getMessage('quiz.completed', {
                emoji: resultEmoji,
                score: score,
                total: totalQuestions,
                percentage: percentage,
                time: timeTaken,
                fileName: session.fileName,
                message: resultMessage
            });

            const keyboard = Markup.inlineKeyboard([
                [Markup.button.callback(this.getMessage('buttons.reviewAnswers'), `show_answers:${session.sessionId}`)],
                [
                    Markup.button.callback(this.getMessage('buttons.downloadPDF'), `download_pdf:${session.sessionId}`),
                    Markup.button.callback(this.getMessage('buttons.saveQuiz'), `save_quiz:${session.sessionId}`)
                ],
                [Markup.button.callback(this.getMessage('buttons.retakeQuiz'), `start_quiz:${session.sessionId}`)]
            ]);

            await ctx.reply(finalMessage, {
                reply_markup: keyboard.reply_markup
            });

            // Update statistics
            await this.updateUserStats(session.userId, {
                quizzesCompleted: 1,
                questionsAnswered: totalQuestions,
                score: score
            });

        } catch (error) {
            logger.error('Error showing quiz results:', error);
            await ctx.reply(this.getMessage('error.processingFailed'));
        }
    }

    /**
     * Generate and send PDF with correct answers
     */
    async generateAndSendPDF(ctx, session) {
        try {
            const PDFDocument = require('pdfkit');
            const fs = require('fs');
            const path = require('path');

            // Create PDF document with normal settings
            const doc = new PDFDocument({ margin: 50 });
            const fileName = `answers_${session.fileName.replace(/[^a-zA-Z0-9]/g, '_')}_${Date.now()}.pdf`;
            const filePath = path.join(__dirname, '../temp', fileName);

            // Ensure temp directory exists
            const tempDir = path.dirname(filePath);
            if (!fs.existsSync(tempDir)) {
                fs.mkdirSync(tempDir, { recursive: true });
            }

            // Pipe PDF to file
            const stream = fs.createWriteStream(filePath);
            doc.pipe(stream);

            // Add title (slightly smaller)
            doc.fontSize(18).text('Quiz Answers', 50, 50);
            doc.fontSize(14).text(`Source: ${session.fileName}`, 50, 80);
            doc.fontSize(12).text(`Generated: ${new Date().toLocaleDateString()}`, 50, 100);

            let yPosition = 140;
            let questionsOnCurrentPage = 0;
            const questionsPerPage = 3;

            // Add questions and answers with 3 questions per page
            session.questions.forEach((question, index) => {
                // Add new page after every 3 questions
                if (questionsOnCurrentPage >= questionsPerPage) {
                    doc.addPage();
                    yPosition = 50;
                    questionsOnCurrentPage = 0;
                }

                // Question number and text (balanced size)
                doc.fontSize(14).fillColor('black').text(`Question ${index + 1}:`, 50, yPosition);
                yPosition += 22;

                const questionText = this.cleanText(question.question) || 'No question text';
                doc.fontSize(11).text(questionText, 50, yPosition, { width: 500 });
                yPosition += Math.max(32, Math.ceil(questionText.length / 75) * 16);

                // Add options based on question type (balanced size)
                if (session.questionType === 'tf') {
                    doc.fontSize(10);
                    doc.text('A) True', 70, yPosition);
                    yPosition += 16;
                    doc.text('B) False', 70, yPosition);
                    yPosition += 22;
                } else if (session.questionType === 'mcq') {
                    const options = this.extractMCQOptions(question);
                    doc.fontSize(10);
                    doc.text(`A) ${this.cleanText(options.A)}`, 70, yPosition);
                    yPosition += 16;
                    doc.text(`B) ${this.cleanText(options.B)}`, 70, yPosition);
                    yPosition += 16;
                    doc.text(`C) ${this.cleanText(options.C)}`, 70, yPosition);
                    yPosition += 16;
                    doc.text(`D) ${this.cleanText(options.D)}`, 70, yPosition);
                    yPosition += 22;
                }

                // Correct answer
                let answerText = question.answer || question.correct_answer || 'No answer';
                if (session.questionType === 'tf') {
                    if (answerText.toLowerCase().includes('true')) {
                        answerText = 'True';
                    } else if (answerText.toLowerCase().includes('false')) {
                        answerText = 'False';
                    } else if (question.explanation) {
                        const explanation = question.explanation.toLowerCase();
                        if (explanation.includes('false') || explanation.includes('incorrect')) {
                            answerText = 'False';
                        } else if (explanation.includes('true') || explanation.includes('correct')) {
                            answerText = 'True';
                        }
                    }
                }

                doc.fontSize(12).fillColor('green').text(`Correct Answer: ${this.cleanText(answerText)}`, 50, yPosition);
                yPosition += 22;

                // Explanation if available (balanced size)
                if (question.explanation) {
                    const explanationText = this.cleanText(question.explanation);
                    doc.fillColor('black').fontSize(10).text(`Explanation: ${explanationText}`, 50, yPosition, { width: 500 });
                    yPosition += Math.max(27, Math.ceil(explanationText.length / 75) * 13);
                }

                yPosition += 22; // Normal space between questions
                questionsOnCurrentPage++; // Increment question counter
            });

            // Finalize PDF
            doc.end();

            // Wait for PDF to be written
            await new Promise((resolve, reject) => {
                stream.on('finish', resolve);
                stream.on('error', reject);
            });

            // Send PDF to user
            await ctx.replyWithDocument({
                source: filePath,
                filename: fileName
            }, {
                caption: `📄 ملف PDF يحتوي على جميع الإجابات الصحيحة\n📚 المصدر: ${session.fileName}\n📊 ${session.questions.length} سؤال`
            });

            // Clean up temp file
            setTimeout(() => {
                try {
                    if (fs.existsSync(filePath)) {
                        fs.unlinkSync(filePath);
                    }
                } catch (error) {
                    logger.debug('Could not delete temp PDF file:', error.message);
                }
            }, 10000);

        } catch (error) {
            logger.error('Error generating PDF:', error);
            await ctx.reply('❌ حدث خطأ في إنشاء ملف PDF. يرجى المحاولة مرة أخرى.');
        }
    }



    /**
     * Extract MCQ options from question object
     */
    extractMCQOptions(question) {
        let options = { A: 'Option A', B: 'Option B', C: 'Option C', D: 'Option D' };

        try {
            if (Array.isArray(question.options)) {
                options.A = question.options[0]?.text || question.options[0] || 'Option A';
                options.B = question.options[1]?.text || question.options[1] || 'Option B';
                options.C = question.options[2]?.text || question.options[2] || 'Option C';
                options.D = question.options[3]?.text || question.options[3] || 'Option D';
            } else if (question.options && typeof question.options === 'object') {
                options.A = question.options.A || question.options.a || 'Option A';
                options.B = question.options.B || question.options.b || 'Option B';
                options.C = question.options.C || question.options.c || 'Option C';
                options.D = question.options.D || question.options.d || 'Option D';
            } else if (question.optionA || question.option_a) {
                options.A = question.optionA || question.option_a || 'Option A';
                options.B = question.optionB || question.option_b || 'Option B';
                options.C = question.optionC || question.option_c || 'Option C';
                options.D = question.optionD || question.option_d || 'Option D';
            }
        } catch (error) {
            logger.error('Error extracting MCQ options:', error);
        }

        return options;
    }

    /**
     * Save quiz for user
     */
    async saveQuizForUser(ctx, session) {
        try {
            const userId = ctx.from.id.toString();

            // Check if user is paid (for paid feature)
            const isPaidUser = await this.checkIfUserIsPaid(userId);

            if (!isPaidUser) {
                const keyboard = Markup.inlineKeyboard([
                    [Markup.button.callback(this.getMessage('buttons.backToMenu'), 'main_menu')]
                ]);

                // Try to edit the message if it's from a callback query, otherwise reply
                if (ctx.callbackQuery) {
                    try {
                        await ctx.editMessageText(this.getMessage('savedQuizzes.paidOnly'), { reply_markup: keyboard.reply_markup });
                    } catch (error) {
                        await ctx.reply(this.getMessage('savedQuizzes.paidOnly'), { reply_markup: keyboard.reply_markup });
                    }
                } else {
                    await ctx.reply(this.getMessage('savedQuizzes.paidOnly'), { reply_markup: keyboard.reply_markup });
                }
                return;
            }

            // Get user info
            const user = ctx.from;
            const username = user.username || `user_${user.id}`;
            const userDisplayName = user.first_name + (user.last_name ? ` ${user.last_name}` : '');

            // Create quiz title from filename
            let title = session.fileName;
            if (title === 'Direct Text Input') {
                title = `Text Quiz - ${new Date().toLocaleDateString('ar-SA')}`;
            } else if (title.length > 50) {
                title = title.substring(0, 50) + '...';
            }

            // Create quiz data for savedQuizzesService
            const quizData = {
                userId: userId,
                title: title,
                content: session.content || '',
                question_type: session.questionType,
                questions: session.questions,
                metadata: {
                    fileName: session.fileName,
                    fileType: session.fileType,
                    username: username,
                    userDisplayName: userDisplayName,
                    quizResults: session.quizState ? {
                        score: session.quizState.score,
                        totalQuestions: session.questions.length,
                        answers: session.quizState.answers,
                        timeTaken: session.quizState.endTime ?
                            Math.floor((session.quizState.endTime - session.quizState.startTime) / 1000) : null
                    } : null
                }
            };

            // Use savedQuizzesService to save the quiz
            const savedQuizzesService = require('./savedQuizzesService');
            const result = savedQuizzesService.addSavedQuiz(quizData);

            if (result.success) {
                const savedDate = new Date().toLocaleDateString('ar-SA');
                const message = this.getMessage('savedQuizzes.saved', {
                    title: title,
                    count: session.questions.length,
                    date: savedDate
                });

                const keyboard = Markup.inlineKeyboard([
                    [Markup.button.callback(this.getMessage('buttons.savedQuizzes'), 'saved_quizzes')],
                    [Markup.button.callback(this.getMessage('buttons.mainMenu'), 'main_menu')]
                ]);

                // Try to edit the message if it's from a callback query, otherwise reply
                if (ctx.callbackQuery) {
                    try {
                        await ctx.editMessageText(message, { reply_markup: keyboard.reply_markup });
                    } catch (error) {
                        // If editing fails, send a new message
                        await ctx.reply(message, { reply_markup: keyboard.reply_markup });
                    }
                } else {
                    await ctx.reply(message, { reply_markup: keyboard.reply_markup });
                }
            } else {
                await ctx.reply('❌ حدث خطأ في حفظ الاختبار. يرجى المحاولة مرة أخرى.');
            }

        } catch (error) {
            logger.error('Error saving quiz:', error);
            await ctx.reply('❌ حدث خطأ في حفظ الاختبار. يرجى المحاولة مرة أخرى.');
        }
    }

    /**
     * Clean text by removing unwanted escape characters and formatting
     */
    cleanText(text) {
        if (!text) return '';

        // Convert to string if not already
        text = String(text);

        // Remove backslash escapes that show up in display
        let cleaned = text
            .replace(/\\([()[\]{}])/g, '$1')  // Remove backslashes before parentheses and brackets
            .replace(/\\\./g, '.')           // Remove backslashes before periods
            .replace(/\\-/g, '-')            // Remove backslashes before hyphens
            .replace(/\\\*/g, '*')           // Remove backslashes before asterisks
            .replace(/\\\\/g, '\\');         // Convert double backslashes to single

        // Fix common encoding issues
        cleaned = cleaned
            .replace(/[""]/g, '"')           // Replace smart quotes
            .replace(/['']/g, "'")           // Replace smart apostrophes
            .replace(/…/g, '...')            // Replace ellipsis
            .replace(/–/g, '-')              // Replace en dash
            .replace(/—/g, '-')              // Replace em dash
            .replace(/β/g, 'beta')           // Replace beta symbol
            .replace(/α/g, 'alpha')          // Replace alpha symbol
            .replace(/γ/g, 'gamma')          // Replace gamma symbol
            .replace(/δ/g, 'delta')          // Replace delta symbol
            .replace(/μ/g, 'micro')          // Replace micro symbol
            .replace(/°/g, ' degrees')       // Replace degree symbol
            .replace(/±/g, '+/-')            // Replace plus-minus symbol
            .replace(/×/g, 'x')              // Replace multiplication symbol
            .replace(/÷/g, '/')              // Replace division symbol
            .replace(/≤/g, '<=')             // Replace less than or equal
            .replace(/≥/g, '>=')             // Replace greater than or equal
            .replace(/≠/g, '!=')             // Replace not equal
            .replace(/™/g, 'TM')             // Replace trademark
            .replace(/®/g, 'R')              // Replace registered trademark
            .replace(/©/g, 'C');             // Replace copyright

        // Remove any remaining non-printable characters except basic punctuation
        cleaned = cleaned.replace(/[^\x20-\x7E\s]/g, '');

        return cleaned.trim();
    }

    /**
     * Escape Markdown special characters to prevent parsing errors
     * But don't escape parentheses in explanations to avoid \( \) display
     */
    escapeMarkdown(text) {
        if (!text) return '';
        // Don't escape parentheses to avoid \( \) in display
        return text.replace(/[_*[\]~`>#+=|{}.!-]/g, '\\$&');
    }

    /**
     * Get file type from Telegram file info
     */
    getFileType(fileInfo) {
        const extension = this.getFileExtension(fileInfo).toLowerCase();

        // Image files (OCR processing)
        if (['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.gif'].includes(extension)) {
            return 'image';
        }
        // PDF files
        else if (['.pdf'].includes(extension)) {
            return 'pdf';
        }
        // Word documents
        else if (['.docx', '.doc'].includes(extension)) {
            return 'document';
        }
        // Excel spreadsheets
        else if (['.xlsx', '.xls'].includes(extension)) {
            return 'spreadsheet';
        }
        // PowerPoint presentations
        else if (['.pptx', '.ppt'].includes(extension)) {
            return 'presentation';
        }
        // Text files
        else if (['.txt', '.csv', '.md', '.json', '.xml', '.html', '.htm'].includes(extension)) {
            return 'text';
        }

        return 'unknown';
    }

    /**
     * Save user information to database (with throttling to reduce spam)
     */
    async saveUser(user) {
        try {
            // Throttle user saves to once every 30 seconds per user to reduce spam during quiz sessions
            const userId = user.id.toString();
            const now = Date.now();

            if (!this.lastUserSave) this.lastUserSave = new Map();

            const lastSave = this.lastUserSave.get(userId);
            if (lastSave && (now - lastSave) < 30000) {
                // Skip save if less than 30 seconds since last save for this user
                return;
            }

            // Save to both SQLite (for bot functionality) and UserManager (for admin panel)
            await this.saveToBothDatabases(user);

            // Update last save time
            this.lastUserSave.set(userId, now);

        } catch (error) {
            logger.error('Error saving user:', error);
        }
    }

    /**
     * Save user to both SQLite and UserManager
     */
    async saveToBothDatabases(user) {
        try {
            // Check if this is a new user BEFORE saving
            let isNewUser = false;
            const db = database.db();

            if (db) {
                // Check if user exists
                await new Promise((resolve) => {
                    db.get('SELECT id FROM users WHERE id = ?', [user.id], (err, row) => {
                        if (err) {
                            logger.error('Error checking user existence:', err);
                            isNewUser = false; // Assume existing user on error
                        } else {
                            isNewUser = !row; // New user if no row found
                            if (isNewUser) {
                                logger.user(`New user detected: ${user.first_name} (${user.id})`);
                            }
                        }
                        resolve();
                    });
                });

                // Save to SQLite database
                const userData = {
                    id: user.id,
                    username: user.username || '',
                    first_name: user.first_name || '',
                    last_name: user.last_name || '',
                    language_code: user.language_code || 'en',
                    is_bot: user.is_bot || false,
                    joined_at: new Date().toISOString(),
                    last_activity: new Date().toISOString()
                };

                // Insert or update user in SQLite
                db.run(`
                    INSERT OR REPLACE INTO users (
                        id, username, first_name, last_name, language_code,
                        is_bot, joined_at, last_activity
                    ) VALUES (?, ?, ?, ?, ?, ?,
                        COALESCE((SELECT joined_at FROM users WHERE id = ?), ?),
                        ?)
                `, [
                    userData.id, userData.username, userData.first_name, userData.last_name,
                    userData.language_code, userData.is_bot, userData.id, userData.joined_at,
                    userData.last_activity
                ]);
            }

            // Save to UserManager (for admin panel) - use the same instance as userService
            try {
                const userService = require('../main/userService');
                if (userService && userService.userManager) {
                    await userService.userManager.addOrUpdateUser(user);
                    logger.info(`User ${user.id} (${user.first_name}) saved to UserManager`);
                } else {
                    // Fallback to direct UserManager access
                    const UserManager = require('../database/userManager');
                    const userManagerInstance = UserManager.getInstance();
                    if (userManagerInstance) {
                        await userManagerInstance.addOrUpdateUser(user);
                        logger.info(`User ${user.id} (${user.first_name}) saved to UserManager (fallback)`);
                    }
                }

                // Update bot statistics
                await this.updateStats();

                // Note: Bot activity detected, but we don't auto-set isRunning anymore
                // The isRunning status should only be controlled by start/stop buttons

                // Log updated stats for debugging
                logger.debug('Updated bot stats:', this.stats);

                // Notify the main window to update user management only for new users
                if (isNewUser) {
                    await this.notifyUserUpdateForNewUser(user.id);
                }

            } catch (error) {
                logger.error('Error saving user to UserManager:', error);
            }

        } catch (error) {
            logger.error('Error saving user to databases:', error);
        }
    }

    /**
     * Get user statistics
     */
    async getUserStats(userId) {
        try {
            const db = database.db();
            if (!db) return {};

            return new Promise((resolve) => {
                db.get(`
                    SELECT
                        joined_at,
                        last_activity,
                        (SELECT COUNT(*) FROM quiz_sessions WHERE user_id = ?) as questionsGenerated,
                        (SELECT COUNT(*) FROM file_uploads WHERE user_id = ?) as filesProcessed
                    FROM users WHERE id = ?
                `, [userId, userId, userId], (err, row) => {
                    if (err) {
                        logger.error('Error getting user stats:', err);
                        resolve({});
                    } else {
                        resolve(row || {});
                    }
                });
            });
        } catch (error) {
            logger.error('Error getting user stats:', error);
            return {};
        }
    }

    /**
     * Ensure user exists in database
     */
    async ensureUserExists(ctx) {
        try {
            const db = database.db();
            if (!db) return;

            const user = ctx.from;
            const userId = user.id;
            const now = new Date().toISOString();

            return new Promise((resolve) => {
                // First check if user exists
                db.get(`SELECT id FROM users WHERE id = ?`, [userId], (err, row) => {
                    if (err) {
                        logger.error('Error checking user existence:', err);
                        resolve();
                        return;
                    }

                    if (!row) {
                        // User doesn't exist, create them
                        db.run(`
                            INSERT INTO users (
                                id, username, first_name, last_name, language_code,
                                is_bot, joined_at, last_activity, is_banned,
                                user_type, subscription_type, subscription_expires_at
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        `, [
                            userId,
                            user.username || null,
                            user.first_name || '',
                            user.last_name || '',
                            user.language_code || 'en',
                            user.is_bot ? 1 : 0,
                            now,
                            now,
                            0,
                            'free',
                            null,
                            null
                        ], (err) => {
                            if (err) {
                                logger.error('Error creating user:', err);
                            } else {
                                logger.info(`Created new user: ${userId} (${user.username || user.first_name})`);
                            }
                            resolve();
                        });
                    } else {
                        // User exists, update last activity
                        db.run(`UPDATE users SET last_activity = ? WHERE id = ?`, [now, userId], (err) => {
                            if (err) {
                                logger.error('Error updating user activity:', err);
                            }
                            resolve();
                        });
                    }
                });
            });
        } catch (error) {
            logger.error('Error ensuring user exists:', error);
        }
    }

    /**
     * Update expired user to free
     */
    async updateUserToFree(userId) {
        try {
            const db = database.db();
            if (!db) return;

            db.run(`
                UPDATE users
                SET user_type = 'free',
                    subscription_type = NULL,
                    subscription_expires_at = NULL
                WHERE id = ?
            `, [userId], (err) => {
                if (err) {
                    logger.error('Error updating expired user to free:', err);
                } else {
                    logger.info(`Updated expired user ${userId} to free`);
                }
            });
        } catch (error) {
            logger.error('Error updating user to free:', error);
        }
    }

    /**
     * Get user type for rate limiting
     */
    async getUserType(userId) {
        try {
            const db = database.db();
            if (!db) {
                logger.warn('Database not available, defaulting to free user');
                return 'free';
            }

            return new Promise((resolve) => {
                db.get(`
                    SELECT user_type, subscription_type, subscription_expires_at
                    FROM users WHERE id = ?
                `, [userId], (err, row) => {
                    if (err) {
                        logger.error('Error getting user type:', err);
                        resolve('free');
                    } else if (row) {
                        // Check if user has paid subscription and it's not expired
                        if (row.user_type === 'paid' && row.subscription_expires_at) {
                            const expiresAt = new Date(row.subscription_expires_at);
                            const now = new Date();
                            if (expiresAt > now) {
                                logger.info(`User ${userId} is paid (expires: ${expiresAt.toISOString()})`);
                                resolve('paid');
                            } else {
                                logger.info(`User ${userId} subscription expired (${expiresAt.toISOString()})`);
                                // Update user to free if subscription expired
                                this.updateUserToFree(userId);
                                resolve('free');
                            }
                        } else {
                            logger.info(`User ${userId} is free user`);
                            resolve('free');
                        }
                    } else {
                        logger.info(`User ${userId} not found in database, defaulting to free`);
                        resolve('free');
                    }
                });
            });
        } catch (error) {
            logger.error('Error getting user type:', error);
            return 'free';
        }
    }

    /**
     * Update user statistics
     */
    async updateUserStats(userId, stats) {
        try {
            logger.debug(`updateUserStats called for user ${userId}:`, stats);

            // Update bot's overall stats
            this.stats.questionsGenerated += stats.questionsGenerated || 0;
            this.stats.filesProcessed += stats.filesProcessed || 0;

            // Update individual user stats in database
            const db = database.db();
            if (db && userId) {
                // First, let's check and fix the database schema
                await this.ensureCorrectSchema(db);
                // Record file upload if files were processed
                if (stats.filesProcessed && stats.filesProcessed > 0) {
                    logger.debug(`Recording ${stats.filesProcessed} file upload(s) for user ${userId}`);

                    // Insert or update file upload count (using correct column names: user_id, count, reset_at)
                    db.run(`
                        INSERT INTO file_uploads (user_id, count, reset_at)
                        VALUES (?, ?, ?)
                        ON CONFLICT(user_id) DO UPDATE SET
                        count = count + ?,
                        reset_at = ?
                    `, [userId.toString(), stats.filesProcessed, Date.now(), stats.filesProcessed, Date.now()],
                    (err) => {
                        if (err) {
                            logger.error('Error updating file uploads:', err);
                        } else {
                            logger.success(`File upload - User ${userId}: +${stats.filesProcessed}`);
                        }
                    });
                }

                // Record quiz session if questions were generated
                if (stats.questionsGenerated && stats.questionsGenerated > 0) {
                    logger.debug(`Recording ${stats.questionsGenerated} question(s) generated for user ${userId}`);

                    // Insert quiz session record (using correct column names)
                    db.run(`
                        INSERT INTO quiz_sessions (
                            user_id, timestamp, question_type, score_correct, score_total,
                            duration, answers, questions, questions_answered
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    `, [
                        userId.toString(),
                        new Date().toISOString(),
                        'generated',
                        0,
                        stats.questionsGenerated,
                        0,
                        '[]',
                        '[]',
                        stats.questionsGenerated
                    ],
                    (err) => {
                        if (err) {
                            logger.error('Error recording quiz session:', err);
                        } else {
                            logger.info(`Questions generated - User ${userId}: ${stats.questionsGenerated} generated`);
                        }
                    });
                }

                // Record quiz completion if quizzes were completed
                if (stats.quizzesCompleted && stats.quizzesCompleted > 0) {
                    logger.debug(`Recording ${stats.quizzesCompleted} quiz completion(s) for user ${userId}`);

                    // Insert quiz attempt record
                    db.run(`
                        INSERT INTO quiz_attempts (user_id, username, quiz_type, score, total_questions, timestamp)
                        VALUES (?, ?, ?, ?, ?, ?)
                    `, [
                        userId.toString(),
                        'Unknown', // We don't have username here, will be updated by statsService
                        'MCQ', // Default type
                        stats.score || 0,
                        stats.questionsAnswered || 0,
                        Date.now()
                    ],
                    (err) => {
                        if (err) {
                            logger.error('Error recording quiz attempt:', err);
                        } else {
                            const score = stats.score || 0;
                            const total = stats.questionsAnswered || 0;
                            const percentage = total > 0 ? Math.round((score / total) * 100) : 0;
                            logger.success(`Quiz result - User ${userId}: ${score}/${total} (${percentage}%)`);
                        }
                    });
                }

                // Update user's last activity
                db.run(`
                    UPDATE users SET last_activity = ? WHERE id = ?
                `, [Date.now(), userId.toString()], (err) => {
                    if (err) {
                        logger.error('Error updating user last activity:', err);
                    }
                });
            }

        } catch (error) {
            logger.error('Error updating user stats:', error);
        }
    }

    /**
     * Ensure correct database schema for user stats
     */
    async ensureCorrectSchema(db) {
        try {
            // Check quiz_sessions table schema
            db.all("PRAGMA table_info(quiz_sessions)", (err, rows) => {
                if (err) {
                    logger.error('Error checking quiz_sessions schema:', err);
                    return;
                }

                const hasUserId = rows.some(row => row.name === 'user_id');
                // Database schema validation (logging minimized for cleaner output)
                if (!hasUserId) {
                    logger.warn('Adding user_id column to quiz_sessions');
                }

                if (!hasUserId) {
                    logger.info('Adding user_id column to quiz_sessions table...');
                    db.run('ALTER TABLE quiz_sessions ADD COLUMN user_id TEXT', (err) => {
                        if (err && !err.message.includes('duplicate column')) {
                            logger.error('Error adding user_id to quiz_sessions:', err);
                        } else {
                            logger.success('Added user_id column to quiz_sessions');
                        }
                    });
                }
            });

            // Check file_uploads table schema
            db.all("PRAGMA table_info(file_uploads)", (err, rows) => {
                if (err) {
                    logger.error('Error checking file_uploads schema:', err);
                    return;
                }

                const hasUserId = rows.some(row => row.name === 'user_id');
                // Database schema validation (logging minimized for cleaner output)
                if (!hasUserId) {
                    logger.warn('Adding user_id column to file_uploads');
                }

                if (!hasUserId) {
                    logger.info('Adding user_id column to file_uploads table...');
                    db.run('ALTER TABLE file_uploads ADD COLUMN user_id TEXT', (err) => {
                        if (err && !err.message.includes('duplicate column')) {
                            logger.error('Error adding user_id to file_uploads:', err);
                        } else {
                            logger.success('Added user_id column to file_uploads');
                        }
                    });
                }
            });

        } catch (error) {
            logger.error('Error ensuring correct schema:', error);
        }
    }

    /**
     * Update bot statistics
     */
    async updateStats() {
        try {
            // Get stats from UserManager (for admin panel consistency)
            const userManager = require('../database/userManager');
            if (userManager && userManager.getInstance) {
                const userManagerInstance = userManager.getInstance();
                if (userManagerInstance) {
                    const stats = userManagerInstance.getStatistics();
                    this.stats.totalUsers = stats.total || 0;
                    this.stats.activeUsers = stats.active || 0;
                }
            }

            // Also update SQLite stats for backward compatibility
            const db = database.db();
            if (db) {
                // Get total users from SQLite
                db.get('SELECT COUNT(*) as count FROM users', (err, row) => {
                    if (!err && row) {
                        // Use the higher count between SQLite and UserManager
                        this.stats.totalUsers = Math.max(this.stats.totalUsers, row.count);
                    }
                });

                // Get active users (last 24 hours) from SQLite
                const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
                db.get('SELECT COUNT(*) as count FROM users WHERE last_activity > ?', [yesterday], (err, row) => {
                    if (!err && row) {
                        // Use the higher count between SQLite and UserManager
                        this.stats.activeUsers = Math.max(this.stats.activeUsers, row.count);
                    }
                });
            }

        } catch (error) {
            logger.error('Error updating stats:', error);
        }
    }

    /**
     * Notify the main window about new users only
     */
    async notifyUserUpdateForNewUser(userId) {
        try {
            logger.debug('Notifying UI about new user:', userId);

            // Get the main window reference and send update notification
            const { BrowserWindow } = require('electron');
            const mainWindow = BrowserWindow.getAllWindows()[0];

            if (mainWindow && !mainWindow.isDestroyed()) {
                logger.debug('Sending new user notification to UI');
                mainWindow.webContents.send('user-data-updated', { isNewUser: true, userId });
            }

        } catch (error) {
            logger.error('Error notifying new user update:', error);
        }
    }

    /**
     * Get bot uptime
     */
    getUptime() {
        const uptime = Date.now() - this.stats.startTime;
        const hours = Math.floor(uptime / (1000 * 60 * 60));
        const minutes = Math.floor((uptime % (1000 * 60 * 60)) / (1000 * 60));
        return `${hours}h ${minutes}m`;
    }

    /**
     * Get bot status for admin panel
     */
    getStatus() {
        return {
            isRunning: this.isRunning,
            stats: this.stats,
            uptime: this.getUptime(),
            botToken: this.botToken ? '✅ Set' : '❌ Not Set',
            adminIds: this.adminIds,
            requiredChannelId: this.requiredChannelId
        };
    }

    /**
     * Update bot configuration
     */
    updateConfig(config) {
        if (config.botToken) {
            this.botToken = config.botToken;
            process.env.TELEGRAM_BOT_TOKEN = config.botToken;
        }

        if (config.adminIds) {
            this.adminIds = config.adminIds;
            process.env.ADMIN_IDS = config.adminIds.join(',');
        }

        if (config.requiredChannelId) {
            this.requiredChannelId = config.requiredChannelId;
            process.env.REQUIRED_CHANNEL_ID = config.requiredChannelId;
        }

        if (config.adminName) {
            this.adminName = config.adminName;
        }
    }

    /**
     * Handle question type selection
     */
    async handleQuestionTypeSelection(ctx, questionType) {
        try {
            const userId = ctx.from.id;

            // Store the user's question type preference
            this.userQuestionTypes.set(userId, questionType);

            // Get the appropriate message based on question type
            let message;
            if (questionType === 'mcq') {
                message = this.getMessage('questionTypeSelection.mcqSelected');
            } else if (questionType === 'tf') {
                message = this.getMessage('questionTypeSelection.tfSelected');
            }

            // Create keyboard with back to menu option
            const keyboard = Markup.inlineKeyboard([
                [Markup.button.callback(this.getMessage('buttons.backToMenu'), 'main_menu')]
            ]);

            // Edit the message to show the selection confirmation
            await ctx.editMessageText(message, { reply_markup: keyboard.reply_markup });

            logger.info(`User ${userId} selected question type: ${questionType}`);
        } catch (error) {
            logger.error('Error handling question type selection:', error);
            await ctx.reply(this.getMessage('error.general'));
        }
    }

    /**
     * Show main menu to user
     */
    async showMainMenu(ctx, editMessage = false) {
        try {
            const keyboard = Markup.inlineKeyboard([
                [Markup.button.callback(this.getMessage('buttons.selectMCQ'), 'select_question_type:mcq')],
                [Markup.button.callback(this.getMessage('buttons.selectTF'), 'select_question_type:tf')],
                [Markup.button.callback(this.getMessage('buttons.savedQuizzes'), 'saved_quizzes')],
                [Markup.button.callback(this.getMessage('buttons.howItWorks'), 'how_it_works')]
            ]);

            const message = this.getMessage('mainMenu.welcome');

            if (editMessage && ctx.callbackQuery) {
                await ctx.editMessageText(message, { reply_markup: keyboard.reply_markup });
            } else {
                await ctx.reply(message, { reply_markup: keyboard.reply_markup });
            }
        } catch (error) {
            logger.error('Error showing main menu:', error);
            if (editMessage) {
                await ctx.reply(this.getMessage('error.general'));
            } else {
                await ctx.reply(this.getMessage('error.general'));
            }
        }
    }

    /**
     * Show saved quizzes for user
     */
    async showSavedQuizzes(ctx, editMessage = false) {
        try {
            const userId = ctx.from.id.toString();

            // Check if user is paid (you can implement your own logic here)
            const isPaidUser = await this.checkIfUserIsPaid(userId);

            if (!isPaidUser) {
                const keyboard = Markup.inlineKeyboard([
                    [Markup.button.callback(this.getMessage('buttons.backToMenu'), 'main_menu')]
                ]);

                const message = this.getMessage('savedQuizzes.paidOnly');
                if (editMessage && ctx.callbackQuery) {
                    await ctx.editMessageText(message, { reply_markup: keyboard.reply_markup });
                } else {
                    await ctx.reply(message, { reply_markup: keyboard.reply_markup });
                }
                return;
            }

            // Get saved quizzes from database
            const savedQuizzes = await this.getSavedQuizzesForUser(userId);

            if (!savedQuizzes || savedQuizzes.length === 0) {
                const keyboard = Markup.inlineKeyboard([
                    [Markup.button.callback(this.getMessage('buttons.createNewQuiz'), 'create_new_quiz')],
                    [Markup.button.callback(this.getMessage('buttons.backToMenu'), 'main_menu')]
                ]);

                const message = this.getMessage('savedQuizzes.empty');
                if (editMessage && ctx.callbackQuery) {
                    await ctx.editMessageText(message, { reply_markup: keyboard.reply_markup });
                } else {
                    await ctx.reply(message, { reply_markup: keyboard.reply_markup });
                }
                return;
            }

            // Create keyboard with saved quizzes
            const buttons = [];
            for (const quiz of savedQuizzes.slice(0, 10)) { // Limit to 10 quizzes
                const title = quiz.title.length > 30 ? quiz.title.substring(0, 30) + '...' : quiz.title;
                buttons.push([Markup.button.callback(`📝 ${title}`, `load_saved_quiz:${quiz.id}`)]);
            }

            buttons.push([Markup.button.callback(this.getMessage('buttons.backToMenu'), 'main_menu')]);

            const keyboard = Markup.inlineKeyboard(buttons);
            const message = this.getMessage('savedQuizzes.title') + this.getMessage('savedQuizzes.list');

            if (editMessage && ctx.callbackQuery) {
                await ctx.editMessageText(message, { reply_markup: keyboard.reply_markup });
            } else {
                await ctx.reply(message, { reply_markup: keyboard.reply_markup });
            }
        } catch (error) {
            logger.error('Error showing saved quizzes:', error);
            if (editMessage) {
                await ctx.reply(this.getMessage('error.general'));
            } else {
                await ctx.reply(this.getMessage('error.general'));
            }
        }
    }

    /**
     * Show how the bot works
     */
    async showHowItWorks(ctx, editMessage = false) {
        try {
            const keyboard = Markup.inlineKeyboard([
                [Markup.button.callback(this.getMessage('buttons.createNewQuiz'), 'create_new_quiz')],
                [Markup.button.callback(this.getMessage('buttons.backToMenu'), 'main_menu')]
            ]);

            const message = this.getMessage('mainMenu.howItWorks');

            if (editMessage && ctx.callbackQuery) {
                await ctx.editMessageText(message, { reply_markup: keyboard.reply_markup });
            } else {
                await ctx.reply(message, { reply_markup: keyboard.reply_markup });
            }
        } catch (error) {
            logger.error('Error showing how it works:', error);
            if (editMessage) {
                await ctx.reply(this.getMessage('error.general'));
            } else {
                await ctx.reply(this.getMessage('error.general'));
            }
        }
    }

    /**
     * Load a saved quiz for the user
     */
    async loadSavedQuiz(ctx, quizId, editMessage = false) {
        try {
            const userId = ctx.from.id.toString();
            const quiz = await this.getSavedQuizById(quizId, userId);

            if (!quiz) {
                const keyboard = Markup.inlineKeyboard([
                    [Markup.button.callback(this.getMessage('buttons.backToMenu'), 'main_menu')]
                ]);

                const message = this.getMessage('savedQuizzes.loadError');
                if (editMessage && ctx.callbackQuery) {
                    await ctx.editMessageText(message, { reply_markup: keyboard.reply_markup });
                } else {
                    await ctx.reply(message, { reply_markup: keyboard.reply_markup });
                }
                return;
            }

            // Create a new session for the loaded quiz
            const sessionId = `${userId}_${Date.now()}`;
            const session = {
                userId: parseInt(userId),
                content: quiz.content,
                fileName: quiz.title,
                fileType: 'saved_quiz',
                questionType: quiz.question_type,
                questionCount: quiz.questions.length,
                questions: quiz.questions,
                sessionId: sessionId
            };

            this.userSessions.set(sessionId, session);

            // Show quiz options - edit the current message
            const messageId = editMessage && ctx.callbackQuery ? ctx.callbackQuery.message.message_id : null;
            await this.showQuizOrAnswersOptions(ctx, session, messageId);
        } catch (error) {
            logger.error('Error loading saved quiz:', error);
            const keyboard = Markup.inlineKeyboard([
                [Markup.button.callback(this.getMessage('buttons.backToMenu'), 'main_menu')]
            ]);

            const message = this.getMessage('savedQuizzes.loadError');
            if (editMessage && ctx.callbackQuery) {
                await ctx.editMessageText(message, { reply_markup: keyboard.reply_markup });
            } else {
                await ctx.reply(message, { reply_markup: keyboard.reply_markup });
            }
        }
    }

    /**
     * Delete a saved quiz
     */
    async deleteSavedQuiz(ctx, quizId, editMessage = false) {
        try {
            const userId = ctx.from.id.toString();
            const result = await this.deleteSavedQuizById(quizId, userId);

            if (result.success) {
                // Show success message briefly, then refresh the saved quizzes list
                if (editMessage && ctx.callbackQuery) {
                    await ctx.editMessageText(this.getMessage('savedQuizzes.deleted'));
                    // Wait a moment then show the updated list
                    setTimeout(async () => {
                        await this.showSavedQuizzes(ctx, true);
                    }, 1500);
                } else {
                    await ctx.reply(this.getMessage('savedQuizzes.deleted'));
                    await this.showSavedQuizzes(ctx);
                }
            } else {
                const keyboard = Markup.inlineKeyboard([
                    [Markup.button.callback(this.getMessage('buttons.backToMenu'), 'main_menu')]
                ]);

                const message = this.getMessage('savedQuizzes.loadError');
                if (editMessage && ctx.callbackQuery) {
                    await ctx.editMessageText(message, { reply_markup: keyboard.reply_markup });
                } else {
                    await ctx.reply(message, { reply_markup: keyboard.reply_markup });
                }
            }
        } catch (error) {
            logger.error('Error deleting saved quiz:', error);
            const keyboard = Markup.inlineKeyboard([
                [Markup.button.callback(this.getMessage('buttons.backToMenu'), 'main_menu')]
            ]);

            const message = this.getMessage('savedQuizzes.loadError');
            if (editMessage && ctx.callbackQuery) {
                await ctx.editMessageText(message, { reply_markup: keyboard.reply_markup });
            } else {
                await ctx.reply(message, { reply_markup: keyboard.reply_markup });
            }
        }
    }

    /**
     * Check if user is paid (implement your own logic)
     */
    async checkIfUserIsPaid(userId) {
        try {
            // For now, return true for admin users
            const userIdInt = parseInt(userId);
            if (this.adminIds.includes(userIdInt)) {
                return true;
            }

            // TODO: Implement your paid user checking logic here
            // You could check a database field, subscription service, etc.

            // For demo purposes, return false (all users are free)
            return false;
        } catch (error) {
            logger.error('Error checking if user is paid:', error);
            return false;
        }
    }

    /**
     * Get saved quizzes for user from database
     */
    async getSavedQuizzesForUser(userId) {
        try {
            const savedQuizzesService = require('./savedQuizzesService');
            return await savedQuizzesService.getUserQuizzes(userId);
        } catch (error) {
            logger.error('Error getting saved quizzes for user:', error);
            return [];
        }
    }

    /**
     * Get saved quiz by ID
     */
    async getSavedQuizById(quizId, userId) {
        try {
            const savedQuizzesService = require('./savedQuizzesService');
            return await savedQuizzesService.getQuizById(quizId, userId);
        } catch (error) {
            logger.error('Error getting saved quiz by ID:', error);
            return null;
        }
    }

    /**
     * Delete saved quiz by ID
     */
    async deleteSavedQuizById(quizId, userId) {
        try {
            const savedQuizzesService = require('./savedQuizzesService');
            return await savedQuizzesService.deleteQuiz(quizId, userId);
        } catch (error) {
            logger.error('Error deleting saved quiz by ID:', error);
            return { success: false };
        }
    }
}

module.exports = TelegramBotService;
