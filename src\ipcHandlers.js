const { ipcMain, dialog, shell, Notification } = require('electron');
const path = require('path');
const fs = require('fs');
const os = require('os');

// Import existing services (adapted for desktop)
const apiService = require('./services/apiService');
const fileService = require('./services/fileService');
const database = require('./database/database');
const logger = require('./utils/logger');
const GeminiService = require('./services/geminiService');
const AccountManager = require('./services/accountManager');

class IPCHandlers {
    constructor() {
        this.geminiService = new GeminiService();
        this.accountManager = new AccountManager();
        this.telegramBot = null;
        this.authFileWatcher = null;
        this.authTerminalProcess = null;
        this.activeTimeouts = new Set();
        this.activeIntervals = new Set();
        this.setupHandlers();
    }

    setTelegramBot(telegramBot) {
        this.telegramBot = telegramBot;
    }

    /**
     * Cleanup all resources to prevent memory leaks
     */
    cleanup() {
        logger.info('Cleaning up IPC handlers resources...');

        // Clear all timeouts
        for (const timeoutId of this.activeTimeouts) {
            clearTimeout(timeoutId);
        }
        this.activeTimeouts.clear();

        // Clear all intervals
        for (const intervalId of this.activeIntervals) {
            clearInterval(intervalId);
        }
        this.activeIntervals.clear();

        // Close file watcher
        if (this.authFileWatcher) {
            try {
                this.authFileWatcher.close();
                this.authFileWatcher = null;
            } catch (error) {
                logger.debug('Error closing file watcher:', error.message);
            }
        }

        // Cleanup gemini service
        if (this.geminiService) {
            this.geminiService.cleanupAllProcesses();
        }

        // Cleanup account manager
        if (this.accountManager) {
            this.accountManager.cleanup();
        }

        logger.success('IPC handlers resources cleaned up');
    }

    setupHandlers() {
        // File operations
        ipcMain.handle('select-file', async (event, options = {}) => {
            try {
                const defaultFilters = [
                    { name: 'All Supported', extensions: ['pdf', 'docx', 'doc', 'txt', 'jpg', 'jpeg', 'png', 'bmp', 'tiff', 'tif'] },
                    { name: 'Documents', extensions: ['pdf', 'docx', 'doc', 'txt'] },
                    { name: 'Images', extensions: ['jpg', 'jpeg', 'png', 'bmp', 'tiff', 'tif'] },
                    { name: 'PDF Files', extensions: ['pdf'] },
                    { name: 'Word Documents', extensions: ['docx', 'doc'] },
                    { name: 'Text Files', extensions: ['txt'] },
                    { name: 'All Files', extensions: ['*'] }
                ];

                const result = await dialog.showOpenDialog({
                    properties: ['openFile'],
                    filters: options.filters || defaultFilters,
                    title: options.title || 'Select a file to process'
                });

                if (!result.canceled && result.filePaths.length > 0) {
                    const filePath = result.filePaths[0];
                    logger.info(`File selected: ${filePath}`);
                    return { success: true, filePath: filePath };
                }

                return { success: false, error: 'No file selected' };
            } catch (error) {
                logger.error('Error in select-file:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('process-file', async (event, fileData, options = {}) => {
            try {
                // Handle different types of file data
                let filePath;

                if (typeof fileData === 'string') {
                    // Direct file path
                    filePath = fileData;
                } else if (fileData && fileData.path) {
                    // File object with path property
                    filePath = fileData.path;
                } else if (fileData && fileData.filePath) {
                    // File object with filePath property
                    filePath = fileData.filePath;
                } else {
                    throw new Error('Invalid file data provided');
                }

                logger.info(`Processing file: ${filePath}`);
                logger.info(`Question count options:`, options);

                if (!filePath || filePath === 'undefined') {
                    throw new Error('File path is undefined or invalid');
                }

                // Check if file exists
                if (!fs.existsSync(filePath)) {
                    throw new Error(`File not found: ${filePath}`);
                }

                // Use existing file service to extract text with question count options
                const result = await fileService.extractTextFromDocument(filePath, options);

                if (result && result.text) {
                    return {
                        success: true,
                        text: result.text,
                        pageCount: result.pageCount || 1,
                        questionCount: result.questionCount || 5
                    };
                } else {
                    throw new Error('Failed to extract text from file');
                }
            } catch (error) {
                logger.error('Error processing file:', error.message);
                return { success: false, error: error.message };
            }
        });

        // Question generation
        ipcMain.handle('generate-questions', async (event, content, type, count, preferredModel = 'auto') => {
            try {
                logger.info(`🎯 IPC Handler: Generating ${count} ${type} questions`);
                logger.info(`📊 IPC Handler: Content length: ${content?.length || 0} characters`);
                logger.info(`🤖 IPC Handler: Received preferred model: "${preferredModel}" (type: ${typeof preferredModel})`);

                // DEBUG: Log the exact count received
                logger.info(`🔢 IPC Handler: QUESTION COUNT DEBUG - Received count: ${count} (type: ${typeof count})`);

                // If frontend sent 'auto', check if we have a saved preference in settings
                if (preferredModel === 'auto') {
                    try {
                        const settingsPath = path.join(__dirname, 'data', 'settings.json');
                        if (fs.existsSync(settingsPath)) {
                            const settingsData = fs.readFileSync(settingsPath, 'utf8');
                            const savedSettings = JSON.parse(settingsData);
                            if (savedSettings.preferredModel && savedSettings.preferredModel !== 'auto') {
                                preferredModel = savedSettings.preferredModel;
                                logger.info(`IPC Handler: Overriding with saved preference: "${preferredModel}"`);
                            }
                        }
                    } catch (settingsError) {
                        logger.warn(`Could not load settings in IPC handler: ${settingsError.message}`);
                    }
                }

                // Create a mock context object similar to Telegram context
                const mockContext = {
                    from: { id: 'desktop-user' },
                    chat: { id: 'desktop-chat' },
                    reply: (message) => {
                        logger.info('Bot reply:', message);
                        return Promise.resolve();
                    },
                    telegram: {
                        editMessageText: () => Promise.resolve()
                    }
                };

                // Helper function to chunk large content
                const chunkContent = (content, maxChunkSize = 8000) => {
                    if (content.length <= maxChunkSize) {
                        return [content];
                    }

                    const chunks = [];
                    const sentences = content.split(/[.!?]+/);
                    let currentChunk = '';

                    for (const sentence of sentences) {
                        if ((currentChunk + sentence).length > maxChunkSize && currentChunk.length > 0) {
                            chunks.push(currentChunk.trim());
                            currentChunk = sentence;
                        } else {
                            currentChunk += sentence + '.';
                        }
                    }

                    if (currentChunk.trim().length > 0) {
                        chunks.push(currentChunk.trim());
                    }

                    return chunks;
                };

                // Handle large content by chunking
                if (content.length > 10000) {
                    logger.info(`📄 IPC Handler: Large content detected (${content.length} chars), using chunking strategy`);

                    const chunks = chunkContent(content, 8000);
                    const questionsPerChunk = Math.ceil(count / chunks.length);
                    let allQuestions = [];

                    logger.info(`📄 IPC Handler: Processing ${chunks.length} chunks, ~${questionsPerChunk} questions per chunk`);

                    for (let i = 0; i < chunks.length && allQuestions.length < count; i++) {
                        const remainingQuestions = count - allQuestions.length;
                        const questionsForThisChunk = Math.min(questionsPerChunk, remainingQuestions);

                        logger.info(`📄 IPC Handler: Processing chunk ${i + 1}/${chunks.length} (${chunks[i].length} chars, ${questionsForThisChunk} questions)`);

                        try {
                            const chunkQuestions = await apiService.generateQuestionsFromAPI(
                                chunks[i],
                                type,
                                questionsForThisChunk,
                                2, // retries
                                false, // isScanned
                                'desktop-user',
                                'text', // contentType
                                preferredModel // pass the preferred model
                            );

                            if (chunkQuestions && chunkQuestions.length > 0) {
                                allQuestions = allQuestions.concat(chunkQuestions);
                                logger.info(`📄 IPC Handler: Chunk ${i + 1} generated ${chunkQuestions.length} questions (total: ${allQuestions.length})`);
                            }
                        } catch (chunkError) {
                            logger.warn(`⚠️ IPC Handler: Chunk ${i + 1} failed: ${chunkError.message}`);
                            // Continue with other chunks
                        }
                    }

                    // Trim to exact count if we got more than requested
                    if (allQuestions.length > count) {
                        allQuestions = allQuestions.slice(0, count);
                    }

                    if (allQuestions.length > 0) {
                        logger.success(`✅ IPC Handler: Generated ${allQuestions.length} questions from ${chunks.length} chunks (requested: ${count})`);
                        return allQuestions;
                    } else {
                        throw new Error('No questions were generated from any chunks');
                    }
                } else {
                    // Normal processing for smaller content
                    const questions = await apiService.generateQuestionsFromAPI(
                        content,
                        type,
                        count,
                        2, // retries
                        false, // isScanned
                        'desktop-user',
                        'text', // contentType
                        preferredModel // pass the preferred model
                    );

                    if (questions && questions.length > 0) {
                        logger.success(`✅ IPC Handler: Generated ${questions.length} questions (requested: ${count})`);
                        if (questions.length !== count) {
                            logger.warn(`⚠️  IPC Handler: Question count mismatch! Requested: ${count}, Generated: ${questions.length}`);
                        }
                        return questions;
                    } else {
                        throw new Error('No questions were generated');
                    }
                }
            } catch (error) {
                logger.error('Error generating questions:', error);
                throw error;
            }
        });

        // Telegram Bot Management
        // Remove existing handlers to prevent duplicates
        ipcMain.removeHandler('telegram-bot-start');
        ipcMain.removeHandler('telegram-bot-stop');
        ipcMain.removeHandler('telegram-bot-status');
        ipcMain.removeHandler('telegram-bot-force-stop');

        ipcMain.handle('telegram-bot-start', async (event) => {
            try {
                if (!this.telegramBot) {
                    return { success: false, error: 'Telegram bot service not initialized' };
                }

                const result = await this.telegramBot.start();
                logger.info('Telegram bot start result:', result);
                return result;
            } catch (error) {
                logger.error('Error starting Telegram bot:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('telegram-bot-stop', async (event) => {
            try {
                if (!this.telegramBot) {
                    return { success: false, error: 'Telegram bot service not initialized' };
                }

                const result = await this.telegramBot.stop();
                logger.info('Telegram bot stop result:', result);
                return result;
            } catch (error) {
                logger.error('Error stopping Telegram bot:', error);
                return { success: false, error: error.message };
            }
        });

        // Get bot status
        ipcMain.handle('telegram-bot-status', async (event) => {
            try {
                if (!this.telegramBot) {
                    return { success: false, error: 'Telegram bot service not initialized' };
                }

                const status = this.telegramBot.getStatus();
                return { success: true, status };
            } catch (error) {
                logger.error('Error getting bot status:', error);
                return { success: false, error: error.message };
            }
        });

        // Force stop bot (for conflict resolution)
        ipcMain.handle('telegram-bot-force-stop', async (event) => {
            try {
                if (!this.telegramBot) {
                    return { success: false, error: 'Telegram bot service not initialized' };
                }

                const result = await this.telegramBot.forceStop();
                logger.info('Telegram bot force stop result:', result);
                return result;
            } catch (error) {
                logger.error('Error force stopping Telegram bot:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('telegram-bot-config', async (event, config) => {
            try {
                if (!this.telegramBot) {
                    return { success: false, error: 'Telegram bot service not initialized' };
                }

                this.telegramBot.updateConfig(config);

                // Update .env file
                await this.updateEnvFile(config);

                // Save admin name to settings
                if (config.adminName) {
                    const settingsPath = path.join(__dirname, 'data', 'settings.json');
                    let currentSettings = {};

                    try {
                        if (fs.existsSync(settingsPath)) {
                            const settingsData = fs.readFileSync(settingsPath, 'utf8');
                            currentSettings = JSON.parse(settingsData);
                        }
                    } catch (readError) {
                        logger.warn('Could not read existing settings:', readError.message);
                    }

                    if (config.adminName) {
                        currentSettings.adminName = config.adminName;
                    }

                    // Ensure data directory exists
                    const dataDir = path.dirname(settingsPath);
                    if (!fs.existsSync(dataDir)) {
                        fs.mkdirSync(dataDir, { recursive: true });
                    }

                    fs.writeFileSync(settingsPath, JSON.stringify(currentSettings, null, 2));
                }

                logger.info('Telegram bot configuration updated');
                return { success: true, message: 'Configuration updated successfully' };
            } catch (error) {
                logger.error('Error updating Telegram bot config:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('test-bot-configuration', async (event, config) => {
            try {
                const axios = require('axios');

                if (!config.botToken) {
                    return { success: false, error: 'Bot token is required' };
                }

                // Test bot token by getting bot info
                const response = await axios.get(`https://api.telegram.org/bot${config.botToken}/getMe`, {
                    timeout: 10000
                });

                if (response.data && response.data.ok) {
                    return {
                        success: true,
                        botInfo: response.data.result,
                        message: 'Bot configuration test successful'
                    };
                } else {
                    return {
                        success: false,
                        error: 'Invalid bot token or bot not accessible'
                    };
                }
            } catch (error) {
                logger.error('Error testing bot configuration:', error);

                if (error.response && error.response.status === 401) {
                    return { success: false, error: 'Invalid bot token' };
                } else if (error.code === 'ENOTFOUND' || error.code === 'ETIMEDOUT') {
                    return { success: false, error: 'Network error - check your internet connection' };
                } else {
                    return { success: false, error: error.message };
                }
            }
        });

        // User Management
        ipcMain.handle('get-telegram-users', async (event, page = 1, limit = 20) => {
            try {
                const db = database.db();
                if (!db) {
                    return { success: false, error: 'Database not available' };
                }

                const offset = (page - 1) * limit;

                return new Promise((resolve) => {
                    // Get total count
                    db.get('SELECT COUNT(*) as total FROM users', (err, countRow) => {
                        if (err) {
                            resolve({ success: false, error: err.message });
                            return;
                        }

                        // Get users with pagination
                        db.all(`
                            SELECT id, username, first_name, last_name, language_code,
                                   joined_at, last_activity, is_bot
                            FROM users
                            ORDER BY last_activity DESC
                            LIMIT ? OFFSET ?
                        `, [limit, offset], (err, rows) => {
                            if (err) {
                                resolve({ success: false, error: err.message });
                            } else {
                                // Add quiz count for each user
                                const usersWithQuizCount = (rows || []).map(user => {
                                    const quizCount = this.getUserQuizCount(user.username || `user_${user.id}`);
                                    return {
                                        ...user,
                                        quizCount: quizCount
                                    };
                                });

                                resolve({
                                    success: true,
                                    users: usersWithQuizCount,
                                    total: countRow.total,
                                    page: page,
                                    totalPages: Math.ceil(countRow.total / limit)
                                });
                            }
                        });
                    });
                });
            } catch (error) {
                logger.error('Error getting Telegram users:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('ban-telegram-user', async (event, userId) => {
            try {
                const db = database.db();
                if (!db) {
                    return { success: false, error: 'Database not available' };
                }

                return new Promise((resolve) => {
                    db.run('UPDATE users SET is_banned = 1 WHERE id = ?', [userId], function(err) {
                        if (err) {
                            resolve({ success: false, error: err.message });
                        } else {
                            logger.info(`User ${userId} banned`);
                            resolve({ success: true, message: 'User banned successfully' });
                        }
                    });
                });
            } catch (error) {
                logger.error('Error banning user:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('unban-telegram-user', async (event, userId) => {
            try {
                const db = database.db();
                if (!db) {
                    return { success: false, error: 'Database not available' };
                }

                return new Promise((resolve) => {
                    db.run('UPDATE users SET is_banned = 0 WHERE id = ?', [userId], function(err) {
                        if (err) {
                            resolve({ success: false, error: err.message });
                        } else {
                            logger.info(`User ${userId} unbanned`);
                            resolve({ success: true, message: 'User unbanned successfully' });
                        }
                    });
                });
            } catch (error) {
                logger.error('Error unbanning user:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('upgrade-telegram-user', async (event, userId, subscriptionType = 'monthly') => {
            try {
                const db = database.db();
                if (!db) {
                    return { success: false, error: 'Database not available' };
                }

                const days = subscriptionType === 'yearly' ? 365 : 30;
                const expiresAt = new Date(Date.now() + days * 24 * 60 * 60 * 1000).toISOString();

                return new Promise((resolve) => {
                    db.run(`
                        UPDATE users
                        SET user_type = 'paid',
                            subscription_type = ?,
                            subscription_expires_at = ?
                        WHERE id = ?
                    `, [subscriptionType, expiresAt, userId], function(err) {
                        if (err) {
                            resolve({ success: false, error: err.message });
                        } else if (this.changes === 0) {
                            resolve({ success: false, error: 'User not found' });
                        } else {
                            logger.info(`User ${userId} upgraded to ${subscriptionType} subscription`);
                            resolve({ success: true, message: 'User upgraded successfully' });
                        }
                    });
                });
            } catch (error) {
                logger.error('Error upgrading user:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('remove-telegram-user', async (event, userId) => {
            try {
                const db = database.db();
                if (!db) {
                    return { success: false, error: 'Database not available' };
                }

                return new Promise((resolve) => {
                    db.run('DELETE FROM users WHERE id = ?', [userId], function(err) {
                        if (err) {
                            resolve({ success: false, error: err.message });
                        } else if (this.changes === 0) {
                            resolve({ success: false, error: 'User not found' });
                        } else {
                            logger.info(`User ${userId} removed from database`);
                            resolve({ success: true, message: 'User removed successfully' });
                        }
                    });
                });
            } catch (error) {
                logger.error('Error removing user:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('add-telegram-user', async (event, userData) => {
            try {
                const db = database.db();
                if (!db) {
                    return { success: false, error: 'Database not available' };
                }

                const { username, subscriptionType, customDays } = userData;

                // Calculate days
                let days;
                switch (subscriptionType) {
                    case 'monthly':
                        days = 30;
                        break;
                    case 'yearly':
                        days = 365;
                        break;
                    case 'custom':
                        days = parseInt(customDays);
                        if (!days || days < 1) {
                            return { success: false, error: 'Invalid custom days value' };
                        }
                        break;
                    default:
                        return { success: false, error: 'Invalid subscription type' };
                }

                // Generate a unique ID for the fake user
                const userId = Date.now();
                const now = new Date().toISOString();
                const expiresAt = new Date(Date.now() + days * 24 * 60 * 60 * 1000).toISOString();

                return new Promise((resolve) => {
                    db.run(`
                        INSERT INTO users (
                            id, username, first_name, last_name, language_code,
                            is_bot, joined_at, last_activity, is_banned,
                            user_type, subscription_type, subscription_expires_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    `, [
                        userId, username, username, '', 'en',
                        0, now, now, 0,
                        'paid', subscriptionType, expiresAt
                    ], function(err) {
                        if (err) {
                            resolve({ success: false, error: err.message });
                        } else {
                            logger.info(`Added new paid user: ${username} (ID: ${userId})`);
                            resolve({
                                success: true,
                                user: {
                                    id: userId,
                                    username: username,
                                    displayName: username,
                                    subscriptionType: subscriptionType,
                                    expiresAt: expiresAt
                                }
                            });
                        }
                    });
                });
            } catch (error) {
                logger.error('Error adding user:', error);
                return { success: false, error: error.message };
            }
        });

        // Get user statistics
        ipcMain.handle('get-user-stats', async (event) => {
            try {
                const db = database.db();
                if (!db) {
                    return { success: false, error: 'Database not available' };
                }

                return new Promise((resolve) => {
                    // Get user statistics
                    db.all(`
                        SELECT
                            COUNT(*) as totalUsers,
                            COUNT(CASE WHEN datetime(last_activity) > datetime('now', '-24 hours') THEN 1 END) as activeUsers,
                            COUNT(CASE WHEN is_banned = 0 AND (user_type = 'free' OR user_type IS NULL) THEN 1 END) as freeUsers,
                            COUNT(CASE WHEN user_type = 'paid' AND (subscription_expires_at IS NULL OR datetime(subscription_expires_at) > datetime('now')) THEN 1 END) as paidUsers
                        FROM users
                    `, [], (err, rows) => {
                        if (err) {
                            logger.error('Error getting user stats:', err);
                            resolve({ success: false, error: err.message });
                        } else {
                            const stats = rows[0] || { totalUsers: 0, activeUsers: 0, freeUsers: 0, paidUsers: 0 };
                            resolve({ success: true, stats });
                        }
                    });
                });
            } catch (error) {
                logger.error('Error getting user stats:', error);
                return { success: false, error: error.message };
            }
        });

        // Get individual user stats for dynamic refresh
        ipcMain.handle('get-individual-user-stats', async (event, userId) => {
            try {
                logger.debug(`Getting individual stats for user ${userId}...`);

                // First get the user data to get username
                const db = database.db();
                if (!db) {
                    return { success: false, error: 'Database not available' };
                }

                // Get user info first
                const user = await new Promise((resolve) => {
                    db.get('SELECT id, username FROM users WHERE id = ?', [userId], (err, row) => {
                        if (err || !row) {
                            resolve({ id: userId, username: null });
                        } else {
                            resolve(row);
                        }
                    });
                });

                // Use the same logic as getUserStatisticsAsync
                const stats = await this.getUserStatisticsAsync(user);

                logger.debug(`Individual stats for user ${userId}:`, stats);
                return { success: true, stats };

            } catch (error) {
                logger.error(`Error getting individual user stats for ${userId}:`, error);
                return { success: false, error: error.message };
            }
        });

        // Get users with statistics, filtering, and search
        ipcMain.handle('get-users-with-stats', async (event, page = 1, limit = 20, filter = 'all', search = '') => {
            try {
                const db = database.db();
                if (!db) {
                    return { success: false, error: 'Database not available' };
                }

                const offset = (page - 1) * limit;
                let whereConditions = [];
                let countParams = [];
                let queryParams = [];

                // Filter conditions
                switch (filter) {
                    case 'free':
                        whereConditions.push('(is_banned = 0 AND (user_type = "free" OR user_type IS NULL))');
                        break;
                    case 'paid':
                        whereConditions.push('(user_type = "paid" AND (subscription_expires_at IS NULL OR datetime(subscription_expires_at) > datetime("now")))');
                        break;
                    case 'expired':
                        whereConditions.push('(user_type = "paid" AND datetime(subscription_expires_at) <= datetime("now"))');
                        break;
                    case 'banned':
                        whereConditions.push('is_banned = 1');
                        break;
                    default:
                        // 'all' - no filter
                        break;
                }

                // Search conditions
                if (search && search.trim()) {
                    const searchTerm = `%${search.trim()}%`;
                    whereConditions.push('(first_name LIKE ? OR last_name LIKE ? OR username LIKE ? OR CAST(id AS TEXT) LIKE ?)');
                    countParams.push(searchTerm, searchTerm, searchTerm, searchTerm);
                    queryParams.push(searchTerm, searchTerm, searchTerm, searchTerm);
                }

                const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : '';

                return new Promise((resolve) => {
                    // Get total count
                    db.get(`SELECT COUNT(*) as total FROM users ${whereClause}`, countParams, (err, countRow) => {
                        if (err) {
                            resolve({ success: false, error: err.message });
                        } else {
                            // Get users with pagination
                            db.all(`
                                SELECT
                                    id,
                                    username,
                                    first_name,
                                    last_name,
                                    language_code,
                                    is_bot,
                                    joined_at,
                                    last_activity,
                                    is_banned,
                                    user_type,
                                    subscription_type,
                                    subscription_expires_at,
                                    CASE
                                        WHEN is_banned = 1 THEN 'banned'
                                        WHEN user_type = 'paid' AND (subscription_expires_at IS NULL OR datetime(subscription_expires_at) > datetime('now')) THEN 'paid'
                                        WHEN user_type = 'paid' AND datetime(subscription_expires_at) <= datetime('now') THEN 'expired'
                                        ELSE 'free'
                                    END as userType
                                FROM users
                                ${whereClause}
                                ORDER BY last_activity DESC
                                LIMIT ? OFFSET ?
                            `, [...queryParams, limit, offset], async (err, rows) => {
                                if (err) {
                                    resolve({ success: false, error: err.message });
                                } else {
                                    try {
                                        // Add comprehensive statistics for each user (async)
                                        const usersWithStats = await Promise.all((rows || []).map(async (user) => {
                                            const stats = await this.getUserStatisticsAsync(user);
                                            return {
                                                ...user,
                                                savedQuizzes: stats.savedQuizzes,
                                                questionsGenerated: stats.questionsGenerated,
                                                quizzesTaken: stats.quizzesTaken,
                                                filesUploaded: stats.filesUploaded,
                                                // Keep backward compatibility
                                                quizCount: stats.savedQuizzes
                                            };
                                        }));

                                        resolve({
                                            success: true,
                                            users: usersWithStats,
                                            total: countRow.total,
                                            page: page,
                                            totalPages: Math.ceil(countRow.total / limit)
                                        });
                                    } catch (statsError) {
                                        console.error('Error getting user statistics:', statsError);
                                        // Fallback to basic user data without stats
                                        resolve({
                                            success: true,
                                            users: (rows || []).map(user => ({
                                                ...user,
                                                savedQuizzes: 0,
                                                questionsGenerated: 0,
                                                quizzesTaken: 0,
                                                filesUploaded: 0,
                                                quizCount: 0
                                            })),
                                            total: countRow.total,
                                            page: page,
                                            totalPages: Math.ceil(countRow.total / limit)
                                        });
                                    }
                                }
                            });
                        }
                    });
                });
            } catch (error) {
                logger.error('Error getting users with stats:', error);
                return { success: false, error: error.message };
            }
        });

        // Get subscription statistics
        ipcMain.handle('get-subscription-stats', async (event) => {
            try {
                // For now, return mock data since we don't have subscription tracking yet
                // This can be enhanced later when subscription system is implemented
                const stats = {
                    monthly: 0,
                    yearly: 0,
                    total: 0
                };

                return { success: true, data: stats };
            } catch (error) {
                logger.error('Error getting subscription stats:', error);
                return { success: false, error: error.message };
            }
        });

        // Write file content (for text files like JSON)
        ipcMain.handle('write-file', async (event, filePath, content) => {
            try {
                const fs = require('fs').promises;
                await fs.writeFile(filePath, content, 'utf8');
                logger.success(`Text file written to: ${filePath}`);
                return { success: true };
            } catch (error) {
                logger.error('Error writing file:', error);
                return { success: false, error: error.message };
            }
        });



        // Mind Map generation
        ipcMain.handle('generate-mind-map', async (event, content, isScanned = false) => {
            try {
                logger.info(`🧠 IPC Handler: Generating mind map`);
                logger.info(`📊 IPC Handler: Content length: ${content?.length || 0} characters`);

                if (!content || content.trim().length === 0) {
                    throw new Error('Content is required for mind map generation');
                }

                if (content.length < 50) {
                    throw new Error('Content is too short for mind map generation (minimum 50 characters)');
                }

                // Use existing API service to generate mind map
                const mindMapData = await apiService.generateMindMapFromAPI(
                    content,
                    isScanned,
                    'desktop-user'
                );

                if (mindMapData && mindMapData.title) {
                    logger.success(`✅ IPC Handler: Generated mind map with title: "${mindMapData.title}"`);
                    return mindMapData;
                } else {
                    throw new Error('No mind map data was generated');
                }
            } catch (error) {
                logger.error('Error generating mind map:', error);
                throw error;
            }
        });

        // Quiz operations
        ipcMain.handle('start-quiz', async (event, questions) => {
            try {
                // Initialize quiz session in database if needed
                const quizSession = {
                    id: Date.now().toString(),
                    questions: questions,
                    startTime: new Date().toISOString(),
                    status: 'active'
                };

                // Store session (you might want to implement this in database)
                logger.info('Quiz session started');
                return { success: true, sessionId: quizSession.id };
            } catch (error) {
                logger.error('Error starting quiz:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('submit-answer', async (event, questionIndex, answer) => {
            try {
                // Process answer submission
                logger.info(`Answer submitted for question ${questionIndex}: ${answer}`);
                return { success: true };
            } catch (error) {
                logger.error('Error submitting answer:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('get-quiz-results', async (event) => {
            try {
                // Get quiz results from database
                return { success: true, results: {} };
            } catch (error) {
                logger.error('Error getting quiz results:', error);
                return { success: false, error: error.message };
            }
        });

        // Database operations
        ipcMain.handle('save-quiz-session', async (event, session) => {
            try {
                logger.info('Saving quiz session:', JSON.stringify(session, null, 2));

                // Save quiz session to database
                const sessionData = {
                    timestamp: session.timestamp,
                    question_type: session.question_type || session.questionType, // Handle both formats
                    score_correct: session.score.correct,
                    score_total: session.score.total,
                    duration: session.duration,
                    answers: JSON.stringify(session.answers),
                    questions: JSON.stringify(session.questions),
                    source_file: session.source_file || null,
                    quiz_title: session.quiz_title || null,
                    completion_status: session.completion_status || 'completed',
                    questions_answered: session.questions_answered || session.score.total,
                    current_question_index: session.current_question_index || session.score.total
                };

                logger.info('Processed session data:', JSON.stringify(sessionData, null, 2));

                // Check if this is a retake (update existing) or new quiz (insert)
                const isRetake = session.retake_session_id;
                logger.info(`Quiz save mode: ${isRetake ? 'UPDATE' : 'INSERT'} ${isRetake ? `(ID: ${session.retake_session_id})` : ''}`);

                // Use existing database methods with sqlite3 API
                const db = database.db();

                // Ensure table exists first
                await new Promise((resolve, reject) => {
                    db.exec(`
                        CREATE TABLE IF NOT EXISTS quiz_sessions (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            timestamp TEXT NOT NULL,
                            question_type TEXT NOT NULL,
                            score_correct INTEGER NOT NULL,
                            score_total INTEGER NOT NULL,
                            duration INTEGER NOT NULL,
                            answers TEXT NOT NULL,
                            questions TEXT NOT NULL,
                            source_file TEXT,
                            quiz_title TEXT
                        )
                    `, (err) => {
                        if (err) reject(err);
                        else resolve();
                    });
                });

                // Insert or Update session data based on whether it's a retake
                const result = await new Promise((resolve, reject) => {
                    if (isRetake) {
                        // Update existing session
                        db.run(`
                            UPDATE quiz_sessions SET
                                timestamp = ?,
                                score_correct = ?,
                                score_total = ?,
                                duration = ?,
                                answers = ?,
                                quiz_title = ?,
                                completion_status = ?,
                                questions_answered = ?,
                                current_question_index = ?
                            WHERE id = ?
                        `, [
                            sessionData.timestamp,
                            sessionData.score_correct,
                            sessionData.score_total,
                            sessionData.duration,
                            sessionData.answers,
                            sessionData.quiz_title,
                            sessionData.completion_status,
                            sessionData.questions_answered,
                            sessionData.current_question_index,
                            session.retake_session_id
                        ], function(err) {
                            if (err) {
                                logger.error('Database update error:', err);
                                reject(err);
                            } else {
                                logger.info(`Quiz session updated for ID: ${session.retake_session_id}`);
                                resolve({ sessionId: session.retake_session_id });
                            }
                        });
                    } else {
                        // Insert new session
                        db.run(`
                            INSERT INTO quiz_sessions
                            (timestamp, question_type, score_correct, score_total, duration, answers, questions, source_file, quiz_title, completion_status, questions_answered, current_question_index)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        `, [
                            sessionData.timestamp,
                            sessionData.question_type,
                            sessionData.score_correct,
                            sessionData.score_total,
                            sessionData.duration,
                            sessionData.answers,
                            sessionData.questions,
                            sessionData.source_file,
                            sessionData.quiz_title,
                            sessionData.completion_status,
                            sessionData.questions_answered,
                            sessionData.current_question_index
                        ], function(err) {
                            if (err) {
                                logger.error('Database insert error:', err);
                                reject(err);
                            } else {
                                logger.info(`Quiz session saved with ID: ${this.lastID}`);
                                resolve({ sessionId: this.lastID });
                            }
                        });
                    }
                });

                logger.success('Quiz session saved to database');
                return { success: true, sessionId: result.sessionId };
            } catch (error) {
                logger.error('Error saving quiz session:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('get-quiz-history', async (event) => {
            try {
                const db = database.db();

                // Ensure table exists first
                await new Promise((resolve, reject) => {
                    db.exec(`
                        CREATE TABLE IF NOT EXISTS quiz_sessions (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            timestamp TEXT NOT NULL,
                            question_type TEXT NOT NULL,
                            score_correct INTEGER NOT NULL,
                            score_total INTEGER NOT NULL,
                            duration INTEGER NOT NULL,
                            answers TEXT NOT NULL,
                            questions TEXT NOT NULL,
                            source_file TEXT,
                            quiz_title TEXT
                        )
                    `, (err) => {
                        if (err) reject(err);
                        else resolve();
                    });
                });

                // Get sessions using sqlite3 API
                const sessions = await new Promise((resolve, reject) => {
                    db.all(`
                        SELECT * FROM quiz_sessions
                        ORDER BY timestamp DESC
                        LIMIT 50
                    `, [], (err, rows) => {
                        if (err) {
                            logger.error('Database query error:', err);
                            reject(err);
                        } else {
                            resolve(rows || []);
                        }
                    });
                });

                // Parse JSON fields with null safety
                const parsedSessions = sessions.map(session => {
                    if (!session) return null;

                    try {
                        return {
                            ...session,
                            answers: session.answers ? JSON.parse(session.answers) : [],
                            questions: session.questions ? JSON.parse(session.questions) : []
                        };
                    } catch (parseError) {
                        logger.error('Error parsing session data:', parseError);
                        return {
                            ...session,
                            answers: [],
                            questions: []
                        };
                    }
                }).filter(session => session !== null);

                logger.info(`Retrieved ${parsedSessions.length} quiz sessions`);
                return { success: true, sessions: parsedSessions };
            } catch (error) {
                logger.error('Error getting quiz history:', error);
                return { success: false, error: error.message, sessions: [] };
            }
        });

        ipcMain.handle('get-statistics', async (event) => {
            try {
                const db = database.db();

                // Ensure table exists first
                await new Promise((resolve, reject) => {
                    db.exec(`
                        CREATE TABLE IF NOT EXISTS quiz_sessions (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            timestamp TEXT NOT NULL,
                            question_type TEXT NOT NULL,
                            score_correct INTEGER NOT NULL,
                            score_total INTEGER NOT NULL,
                            duration INTEGER NOT NULL,
                            answers TEXT NOT NULL,
                            questions TEXT NOT NULL
                        )
                    `, (err) => {
                        if (err) reject(err);
                        else resolve();
                    });
                });

                // Get basic statistics using sqlite3 API
                const totalQuizzes = await new Promise((resolve, reject) => {
                    db.get('SELECT COUNT(*) as count FROM quiz_sessions', [], (err, row) => {
                        if (err) reject(err);
                        else resolve(row || { count: 0 });
                    });
                });

                const avgScore = await new Promise((resolve, reject) => {
                    db.get('SELECT AVG(CAST(score_correct AS FLOAT) / CAST(score_total AS FLOAT) * 100) as avg FROM quiz_sessions WHERE score_total > 0', [], (err, row) => {
                        if (err) reject(err);
                        else resolve(row || { avg: 0 });
                    });
                });

                const totalQuestions = await new Promise((resolve, reject) => {
                    db.get('SELECT SUM(score_total) as total FROM quiz_sessions', [], (err, row) => {
                        if (err) reject(err);
                        else resolve(row || { total: 0 });
                    });
                });

                const stats = {
                    totalQuizzes: totalQuizzes.count || 0,
                    averageScore: Math.round(avgScore.avg || 0),
                    totalQuestions: totalQuestions.total || 0,
                    lastQuizDate: null
                };

                // Get last quiz date
                const lastQuiz = await new Promise((resolve, reject) => {
                    db.get('SELECT timestamp FROM quiz_sessions ORDER BY timestamp DESC LIMIT 1', [], (err, row) => {
                        if (err) reject(err);
                        else resolve(row);
                    });
                });

                if (lastQuiz) {
                    stats.lastQuizDate = lastQuiz.timestamp;
                }

                logger.info(`Retrieved statistics: ${JSON.stringify(stats)}`);
                return { success: true, statistics: stats };
            } catch (error) {
                logger.error('Error getting statistics:', error);
                return { success: false, error: error.message, statistics: null };
            }
        });

        // Get overall application statistics for sidebar
        ipcMain.handle('get-overall-stats', async (event) => {
            try {
                const db = database.db();

                if (!db) {
                    console.log('Database not available for overall stats');
                    return {
                        success: false,
                        error: 'Database not available',
                        stats: { questionsGenerated: 0, filesProcessed: 0 }
                    };
                }

                // Get total questions generated from quiz_sessions
                const questionsGenerated = await new Promise((resolve) => {
                    db.get('SELECT SUM(score_total) as total FROM quiz_sessions', [], (err, row) => {
                        if (err) {
                            console.log('Quiz sessions table not found or error:', err.message);
                            resolve(0);
                        } else {
                            const total = row?.total || 0;
                            // Only log if value changed or every 5 minutes
                            if (!this.lastQuestionsTotal || this.lastQuestionsTotal !== total ||
                                !this.lastQuestionsLogTime || (Date.now() - this.lastQuestionsLogTime) > 300000) {
                                const chalk = require('chalk');
                                console.log(chalk.bgBlue.white.bold(' 📊 STATS ') + chalk.blue.bold(' Questions: ') + chalk.yellow.bold(total));
                                this.lastQuestionsTotal = total;
                                this.lastQuestionsLogTime = Date.now();
                            }
                            resolve(total);
                        }
                    });
                });

                // Get total files processed from file_uploads
                const filesProcessed = await new Promise((resolve) => {
                    db.get('SELECT SUM(count) as total FROM file_uploads', [], (err, row) => {
                        if (err) {
                            console.log('File uploads table not found or error:', err.message);
                            resolve(0);
                        } else {
                            const total = row?.total || 0;
                            // Only log if value changed or every 5 minutes
                            if (!this.lastFilesTotal || this.lastFilesTotal !== total ||
                                !this.lastFilesLogTime || (Date.now() - this.lastFilesLogTime) > 300000) {
                                const chalk = require('chalk');
                                console.log(chalk.bgBlue.white.bold(' 📊 STATS ') + chalk.blue.bold(' Files: ') + chalk.yellow.bold(total));
                                this.lastFilesTotal = total;
                                this.lastFilesLogTime = Date.now();
                            }
                            resolve(total);
                        }
                    });
                });

                // Alternative: Get questions from quiz_attempts table if quiz_sessions doesn't exist
                let alternativeQuestions = 0;
                if (questionsGenerated === 0) {
                    alternativeQuestions = await new Promise((resolve) => {
                        db.get('SELECT COUNT(*) as count FROM quiz_attempts', [], (err, row) => {
                            if (err) {
                                console.log('Quiz attempts table not found:', err.message);
                                resolve(0);
                            } else {
                                const count = row?.count || 0;
                                console.log('Alternative questions count from quiz_attempts:', count);
                                resolve(count);
                            }
                        });
                    });
                }

                const finalQuestionsCount = questionsGenerated || alternativeQuestions;

                const overallStats = {
                    questionsGenerated: finalQuestionsCount,
                    filesProcessed: filesProcessed || 0
                };

                // Only log final stats if values changed
                if (!this.lastFinalStats ||
                    this.lastFinalStats.questionsGenerated !== overallStats.questionsGenerated ||
                    this.lastFinalStats.filesProcessed !== overallStats.filesProcessed) {
                    console.log('📊 Final overall stats:', overallStats);
                    logger.info(`Retrieved overall stats: ${JSON.stringify(overallStats)}`);
                    this.lastFinalStats = overallStats;
                }
                return { success: true, stats: overallStats };
            } catch (error) {
                console.error('Error getting overall statistics:', error);
                logger.error('Error getting overall statistics:', error);
                return {
                    success: false,
                    error: error.message,
                    stats: { questionsGenerated: 0, filesProcessed: 0 }
                };
            }
        });

        // Clear all quiz history
        ipcMain.handle('clear-quiz-history', async (event) => {
            try {
                const db = database.db();

                // Delete all records from quiz_sessions table
                await new Promise((resolve, reject) => {
                    db.run('DELETE FROM quiz_sessions', [], function(err) {
                        if (err) {
                            logger.error('Database delete error:', err);
                            reject(err);
                        } else {
                            logger.info(`Cleared ${this.changes} quiz sessions from history`);
                            resolve();
                        }
                    });
                });

                return { success: true, message: 'Quiz history cleared successfully' };
            } catch (error) {
                logger.error('Error clearing quiz history:', error);
                return { success: false, error: error.message };
            }
        });

        // Clear single quiz from history
        ipcMain.handle('clear-single-quiz-history', async (event, sessionId) => {
            try {
                const db = database.db();

                // Delete specific record from quiz_sessions table
                await new Promise((resolve, reject) => {
                    db.run('DELETE FROM quiz_sessions WHERE id = ?', [sessionId], function(err) {
                        if (err) {
                            logger.error('Database delete error:', err);
                            reject(err);
                        } else {
                            logger.info(`Cleared quiz session ${sessionId} from history`);
                            resolve();
                        }
                    });
                });

                return { success: true, message: 'Quiz removed from history successfully' };
            } catch (error) {
                logger.error('Error clearing single quiz from history:', error);
                return { success: false, error: error.message };
            }
        });











        // API Key Management
        ipcMain.handle('get-api-key-info', async (event) => {
            try {
                const currentKey = process.env.API_KEY;

                if (!currentKey) {
                    return {
                        success: false,
                        error: 'No API key configured',
                        hasKey: false
                    };
                }

                // Mask the key for security (show first 15 and last 4 characters)
                const maskedKey = currentKey.length > 20
                    ? `${currentKey.substring(0, 15)}...${currentKey.substring(currentKey.length - 4)}`
                    : '***masked***';

                return {
                    success: true,
                    hasKey: true,
                    maskedKey: maskedKey,
                    keyLength: currentKey.length,
                    isValidFormat: currentKey.startsWith('sk-or-v1-') && currentKey.length === 73,
                    provider: 'OpenRouter.ai'
                };
            } catch (error) {
                logger.error('Error getting API key info:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('update-api-key', async (event, newApiKey) => {
            try {
                // Validate the new API key format
                if (!newApiKey || typeof newApiKey !== 'string') {
                    return { success: false, error: 'Invalid API key format' };
                }

                if (!newApiKey.startsWith('sk-or-v1-')) {
                    return { success: false, error: 'API key must start with "sk-or-v1-"' };
                }

                if (newApiKey.length !== 73) {
                    return { success: false, error: 'API key must be exactly 73 characters long' };
                }

                logger.info('Updating API key in both database and environment...');

                // Step 1: Update the database (primary source)
                const apiService = require('./services/apiService');

                try {
                    await apiService.addApiKey(newApiKey);
                    logger.info('✅ API key updated in database successfully');
                } catch (dbError) {
                    logger.error('❌ Failed to update database:', dbError.message);
                    return { success: false, error: `Database update failed: ${dbError.message}` };
                }

                // Step 2: Update the .env file (backup/sync)
                const envPath = path.join(__dirname, '..', '.env');
                let envContent = '';

                try {
                    if (fs.existsSync(envPath)) {
                        envContent = fs.readFileSync(envPath, 'utf8');
                    }
                } catch (readError) {
                    logger.warn('Could not read .env file, creating new one');
                }

                // Update or add the API_KEY line
                const lines = envContent.split('\n');
                let keyUpdated = false;

                for (let i = 0; i < lines.length; i++) {
                    if (lines[i].startsWith('API_KEY=')) {
                        lines[i] = `API_KEY=${newApiKey}`;
                        keyUpdated = true;
                        break;
                    }
                }

                if (!keyUpdated) {
                    lines.push(`API_KEY=${newApiKey}`);
                }

                // Write back to .env file
                try {
                    fs.writeFileSync(envPath, lines.join('\n'), 'utf8');
                    logger.info('✅ API key updated in .env file successfully');
                } catch (writeError) {
                    logger.warn('⚠️  Could not update .env file:', writeError.message);
                }

                // Step 3: Update the environment variable for current session
                process.env.API_KEY = newApiKey;

                logger.info('🎉 API key updated successfully in all locations');
                return {
                    success: true,
                    message: 'API key updated successfully! Database and .env file are now synchronized.'
                };

            } catch (error) {
                logger.error('Error updating API key:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('test-api-key', async (event, testKey = null) => {
            try {
                const keyToTest = testKey || process.env.API_KEY;

                if (!keyToTest) {
                    return { success: false, error: 'No API key to test' };
                }

                // Test the API key with a simple request
                const apiService = require('./services/apiService');

                logger.info('Testing API key with simple request...');

                const testResult = await apiService.generateQuestionsFromAPI(
                    "Test content for API key validation.",
                    "TF",
                    1,
                    0, // retries
                    false, // isScanned
                    'api-key-test',
                    'text',
                    null // Use any available model
                );

                if (testResult && Array.isArray(testResult) && testResult.length > 0) {
                    logger.info('API key test successful');
                    return {
                        success: true,
                        message: 'API key is working correctly',
                        questionsGenerated: testResult.length
                    };
                } else {
                    return {
                        success: false,
                        error: 'API key test failed - no questions generated'
                    };
                }

            } catch (error) {
                logger.error('API key test failed:', error);
                return {
                    success: false,
                    error: `API key test failed: ${error.message}`
                };
            }
        });

        // Get environment configuration
        ipcMain.handle('get-env-config', async (event) => {
            try {
                return {
                    TELEGRAM_BOT_TOKEN: process.env.TELEGRAM_BOT_TOKEN || null,
                    ADMIN_IDS: process.env.ADMIN_IDS || null,
                    REQUIRED_CHANNEL_ID: process.env.REQUIRED_CHANNEL_ID || null
                };
            } catch (error) {
                logger.error('Error getting environment config:', error);
                return null;
            }
        });

        // Update environment configuration (.env file)
        ipcMain.handle('update-env-config', async (event, config) => {
            try {
                const fs = require('fs');
                const path = require('path');
                const envPath = path.join(process.cwd(), '.env');

                let envContent = '';

                // Read existing .env file if it exists
                if (fs.existsSync(envPath)) {
                    envContent = fs.readFileSync(envPath, 'utf8');
                }

                // Update or add environment variables
                const updateEnvVar = (content, key, value) => {
                    const regex = new RegExp(`^${key}=.*$`, 'm');
                    const newLine = value ? `${key}=${value}` : `${key}=`;

                    if (regex.test(content)) {
                        // Update existing line
                        return content.replace(regex, newLine);
                    } else {
                        // Add new line
                        return content + (content.endsWith('\n') ? '' : '\n') + newLine + '\n';
                    }
                };

                // Update each configuration value
                if (config.hasOwnProperty('botToken')) {
                    envContent = updateEnvVar(envContent, 'TELEGRAM_BOT_TOKEN', config.botToken || '');
                }
                if (config.hasOwnProperty('adminIds')) {
                    const adminIdsValue = config.adminIds && config.adminIds.length > 0 ? config.adminIds.join(',') : '';
                    envContent = updateEnvVar(envContent, 'ADMIN_IDS', adminIdsValue);
                }
                if (config.hasOwnProperty('requiredChannelId')) {
                    envContent = updateEnvVar(envContent, 'REQUIRED_CHANNEL_ID', config.requiredChannelId || '');
                }

                // Write updated content back to .env file
                fs.writeFileSync(envPath, envContent, 'utf8');

                // Update process.env for immediate effect
                if (config.hasOwnProperty('botToken')) {
                    process.env.TELEGRAM_BOT_TOKEN = config.botToken || '';
                }
                if (config.hasOwnProperty('adminIds')) {
                    process.env.ADMIN_IDS = config.adminIds && config.adminIds.length > 0 ? config.adminIds.join(',') : '';
                }
                if (config.hasOwnProperty('requiredChannelId')) {
                    process.env.REQUIRED_CHANNEL_ID = config.requiredChannelId || '';
                }

                logger.info('Successfully updated .env file with new configuration');
                return { success: true };
            } catch (error) {
                logger.error('Error updating .env file:', error);
                return { success: false, error: error.message };
            }
        });

        // Test Telegram bot connection
        ipcMain.handle('test-telegram-bot-connection', async (event, botToken) => {
            try {
                const https = require('https');
                const { URL } = require('url');

                if (!botToken) {
                    return {
                        success: false,
                        error: 'No bot token provided',
                        details: 'Bot token is required to test connection'
                    };
                }

                // Test the bot token by calling Telegram's getMe API
                const apiUrl = `https://api.telegram.org/bot${botToken}/getMe`;

                return new Promise((resolve) => {
                    const url = new URL(apiUrl);
                    const options = {
                        hostname: url.hostname,
                        port: url.port || 443,
                        path: url.pathname,
                        method: 'GET',
                        timeout: 10000 // 10 second timeout
                    };

                    const req = https.request(options, (res) => {
                        let data = '';

                        res.on('data', (chunk) => {
                            data += chunk;
                        });

                        res.on('end', () => {
                            try {
                                const response = JSON.parse(data);

                                if (response.ok && response.result) {
                                    const botInfo = response.result;
                                    resolve({
                                        success: true,
                                        botInfo: {
                                            id: botInfo.id,
                                            username: botInfo.username,
                                            firstName: botInfo.first_name,
                                            canJoinGroups: botInfo.can_join_groups,
                                            canReadAllGroupMessages: botInfo.can_read_all_group_messages,
                                            supportsInlineQueries: botInfo.supports_inline_queries
                                        },
                                        message: `Connected successfully to bot: @${botInfo.username}`
                                    });
                                } else {
                                    resolve({
                                        success: false,
                                        error: 'Invalid bot token',
                                        details: response.description || 'The bot token is invalid or the bot does not exist'
                                    });
                                }
                            } catch (parseError) {
                                resolve({
                                    success: false,
                                    error: 'Invalid response from Telegram',
                                    details: 'Failed to parse response from Telegram API'
                                });
                            }
                        });
                    });

                    req.on('error', (error) => {
                        resolve({
                            success: false,
                            error: 'Network connection failed',
                            details: `Failed to connect to Telegram servers: ${error.message}`
                        });
                    });

                    req.on('timeout', () => {
                        req.destroy();
                        resolve({
                            success: false,
                            error: 'Connection timeout',
                            details: 'Request to Telegram servers timed out after 10 seconds'
                        });
                    });

                    req.end();
                });

            } catch (error) {
                logger.error('Error testing Telegram bot connection:', error);
                return {
                    success: false,
                    error: 'Connection test failed',
                    details: error.message
                };
            }
        });

        // Bot Messages Management IPC Handlers
        ipcMain.handle('get-bot-messages', async () => {
            try {
                logger.info('🔄 IPC: Getting bot messages...');

                // Test if we can require the config first
                const { DEFAULT_BOT_MESSAGES } = require('./config/botMessages');
                logger.info(`📋 Default messages loaded: ${Object.keys(DEFAULT_BOT_MESSAGES).length} categories`);

                const botMessagesManager = require('./services/botMessagesManager');
                logger.info('📋 Bot messages manager loaded');

                const messages = botMessagesManager.getAllMessagesFlat();
                logger.info(`✅ IPC: Retrieved ${messages.length} bot messages`);

                // Log first few messages for debugging
                if (messages.length > 0) {
                    logger.info(`📋 Sample messages: ${messages.slice(0, 3).map(m => m.key).join(', ')}`);
                }

                return { success: true, messages };
            } catch (error) {
                logger.error('❌ IPC: Error getting bot messages:', error);
                logger.error('❌ IPC: Error stack:', error.stack);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('update-bot-message', async (event, messageKey, newMessage) => {
            try {
                const botMessagesManager = require('./services/botMessagesManager');
                const result = await botMessagesManager.updateMessage(messageKey, newMessage);
                return result;
            } catch (error) {
                logger.error('Error updating bot message:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('update-bot-messages', async (event, updates) => {
            try {
                const botMessagesManager = require('./services/botMessagesManager');

                // Update each message
                for (const update of updates) {
                    await botMessagesManager.updateMessage(update.key, update.message);
                }

                return { success: true };
            } catch (error) {
                logger.error('Error updating bot messages:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('reset-bot-messages', async () => {
            try {
                const botMessagesManager = require('./services/botMessagesManager');
                const result = await botMessagesManager.resetToDefaults();
                return result;
            } catch (error) {
                logger.error('Error resetting bot messages:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('reload-bot-messages', async () => {
            try {
                // Force clear require cache to reload fresh messages
                delete require.cache[require.resolve('./config/botMessages')];

                // Reset the singleton before clearing cache
                const botMessagesManager = require('./services/botMessagesManager');
                if (botMessagesManager.resetInstance) {
                    botMessagesManager.resetInstance();
                }

                delete require.cache[require.resolve('./services/botMessagesManager')];

                const freshBotMessagesManager = require('./services/botMessagesManager');
                await freshBotMessagesManager.loadCustomMessages();

                // Also reload messages in the Telegram bot if it exists
                if (this.telegramBot) {
                    await this.telegramBot.reloadMessages();
                }

                logger.info('✅ Bot messages reloaded with cache clear');
                return { success: true };
            } catch (error) {
                logger.error('Error reloading bot messages:', error);
                return { success: false, error: error.message };
            }
        });

        // Rate Limits Management
        ipcMain.handle('get-rate-limits', async (event) => {
            try {
                const rateLimitService = require('./services/rateLimitService');
                const limits = rateLimitService.getAllLimits();

                logger.info('📊 Rate limits retrieved:', limits);
                return { success: true, limits };
            } catch (error) {
                logger.error('Error getting rate limits:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('update-rate-limits', async (event, limits) => {
            try {
                const rateLimitService = require('./services/rateLimitService');

                // Update all limits at once (more efficient and ensures atomic save)
                rateLimitService.updateAllLimits(limits);

                logger.info('📊 Rate limits updated and saved:', limits);
                return { success: true, message: 'Rate limits updated and saved successfully' };
            } catch (error) {
                logger.error('Error updating rate limits:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('get-usage-stats', async (event) => {
            try {
                const rateLimitService = require('./services/rateLimitService');
                const stats = rateLimitService.getUsageStats();

                logger.info('📊 Usage statistics retrieved:', stats);
                return { success: true, stats };
            } catch (error) {
                logger.error('Error getting usage statistics:', error);
                return { success: false, error: error.message };
            }
        });

        // Create test users for rate limiting testing
        ipcMain.handle('create-test-users', async (event) => {
            try {
                const db = database.db();
                if (!db) {
                    return { success: false, error: 'Database not available' };
                }

                const now = new Date().toISOString();
                const futureDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(); // 30 days from now

                // Create test free user
                const freeUserId = 999999001;
                await new Promise((resolve) => {
                    db.run(`
                        INSERT OR REPLACE INTO users (
                            id, username, first_name, last_name, language_code,
                            is_bot, joined_at, last_activity, is_banned,
                            user_type, subscription_type, subscription_expires_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    `, [
                        freeUserId, 'test_free_user', 'Test Free', 'User', 'en',
                        0, now, now, 0,
                        'free', null, null
                    ], resolve);
                });

                // Create test paid user
                const paidUserId = 999999002;
                await new Promise((resolve) => {
                    db.run(`
                        INSERT OR REPLACE INTO users (
                            id, username, first_name, last_name, language_code,
                            is_bot, joined_at, last_activity, is_banned,
                            user_type, subscription_type, subscription_expires_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    `, [
                        paidUserId, 'test_paid_user', 'Test Paid', 'User', 'en',
                        0, now, now, 0,
                        'paid', 'monthly', futureDate
                    ], resolve);
                });

                logger.info('Created test users for rate limiting testing');
                return {
                    success: true,
                    message: 'Test users created successfully',
                    users: {
                        free: { id: freeUserId, username: 'test_free_user' },
                        paid: { id: paidUserId, username: 'test_paid_user' }
                    }
                };
            } catch (error) {
                logger.error('Error creating test users:', error);
                return { success: false, error: error.message };
            }
        });

        // Add duration to user subscription
        ipcMain.handle('add-user-duration', async (event, userId, totalMinutes) => {
            try {
                const db = database.db();
                if (!db) {
                    return { success: false, error: 'Database not available' };
                }

                return new Promise((resolve) => {
                    // First check if user exists
                    db.get(`SELECT * FROM users WHERE id = ?`, [userId], (err, user) => {
                        if (err) {
                            resolve({ success: false, error: err.message });
                            return;
                        }

                        if (!user) {
                            resolve({ success: false, error: 'User not found' });
                            return;
                        }

                        // Calculate new expiry date
                        const now = new Date();
                        const currentExpiry = user.subscription_expires_at ? new Date(user.subscription_expires_at) : now;
                        const baseTime = currentExpiry > now ? currentExpiry : now;
                        const newExpiry = new Date(baseTime.getTime() + totalMinutes * 60 * 1000);

                        // Update user subscription with explicit transaction and forced sync
                        db.serialize(() => {
                            db.run('BEGIN IMMEDIATE TRANSACTION');
                            db.run(`
                                UPDATE users
                                SET user_type = 'paid',
                                    subscription_type = COALESCE(subscription_type, 'custom'),
                                    subscription_expires_at = ?
                                WHERE id = ?
                            `, [newExpiry.toISOString(), userId], function(err) {
                                if (err) {
                                    db.run('ROLLBACK');
                                    logger.error(`❌ Failed to update user ${userId}:`, err);
                                    resolve({ success: false, error: err.message });
                                } else {
                                    db.run('COMMIT', (commitErr) => {
                                        if (commitErr) {
                                            logger.error(`❌ Failed to commit transaction for user ${userId}:`, commitErr);
                                            resolve({ success: false, error: commitErr.message });
                                        } else {
                                            // Force database sync to disk
                                            db.run('PRAGMA wal_checkpoint(FULL)', (syncErr) => {
                                                if (syncErr) {
                                                    logger.warn(`⚠️ Warning: Failed to sync database to disk:`, syncErr);
                                                } else {
                                                    logger.info(`💾 Database synced to disk successfully`);
                                                }

                                                logger.info(`✅ USER UPGRADED: User ${userId} is now PAID until ${newExpiry.toISOString()}`);
                                                logger.info(`💾 Database transaction committed and synced`);

                                                // Force refresh Telegram bot user cache and database connection
                                                if (this.telegramBot) {
                                                    this.telegramBot.refreshUserCache(userId);
                                                    this.telegramBot.setDatabaseInstance(db);
                                                }

                                                resolve({
                                                    success: true,
                                                    message: 'Duration added successfully',
                                                    newExpiry: newExpiry.toISOString(),
                                                    userType: 'paid'
                                                });
                                            });
                                        }
                                    });
                                }
                            });
                        });
                    });
                });
            } catch (error) {
                logger.error('Error adding user duration:', error);
                return { success: false, error: error.message };
            }
        });

        // Get user status for duration management
        ipcMain.handle('get-user-status', async (event, userId) => {
            try {
                const db = database.db();
                if (!db) {
                    return { success: false, error: 'Database not available' };
                }

                return new Promise((resolve) => {
                    db.get(`
                        SELECT id, username, first_name, last_name, user_type,
                               subscription_type, subscription_expires_at, joined_at, last_activity
                        FROM users WHERE id = ?
                    `, [userId], (err, user) => {
                        if (err) {
                            resolve({ success: false, error: err.message });
                        } else if (user) {
                            resolve({ success: true, user });
                        } else {
                            resolve({ success: false, error: 'User not found' });
                        }
                    });
                });
            } catch (error) {
                logger.error('Error getting user status:', error);
                return { success: false, error: error.message };
            }
        });

        // Add duration to user subscription by username
        ipcMain.handle('add-user-duration-by-username', async (event, username, totalMinutes) => {
            try {
                const db = database.db();
                if (!db) {
                    return { success: false, error: 'Database not available' };
                }

                return new Promise((resolve) => {
                    // First find user by username
                    db.get(`SELECT * FROM users WHERE username = ?`, [username], (err, user) => {
                        if (err) {
                            resolve({ success: false, error: err.message });
                            return;
                        }

                        if (!user) {
                            resolve({ success: false, error: `User with username '@${username}' not found` });
                            return;
                        }

                        // Calculate new expiry date
                        const now = new Date();
                        const currentExpiry = user.subscription_expires_at ? new Date(user.subscription_expires_at) : now;
                        const baseTime = currentExpiry > now ? currentExpiry : now;
                        const newExpiry = new Date(baseTime.getTime() + totalMinutes * 60 * 1000);

                        // Update user subscription with explicit transaction and forced sync
                        db.serialize(() => {
                            db.run('BEGIN IMMEDIATE TRANSACTION');
                            db.run(`
                                UPDATE users
                                SET user_type = 'paid',
                                    subscription_type = COALESCE(subscription_type, 'custom'),
                                    subscription_expires_at = ?
                                WHERE id = ?
                            `, [newExpiry.toISOString(), user.id], function(err) {
                                if (err) {
                                    db.run('ROLLBACK');
                                    logger.error(`❌ Failed to update user @${username}:`, err);
                                    resolve({ success: false, error: err.message });
                                } else {
                                    db.run('COMMIT', (commitErr) => {
                                        if (commitErr) {
                                            logger.error(`❌ Failed to commit transaction for @${username}:`, commitErr);
                                            resolve({ success: false, error: commitErr.message });
                                        } else {
                                            // Force database sync to disk
                                            db.run('PRAGMA wal_checkpoint(FULL)', (syncErr) => {
                                                if (syncErr) {
                                                    logger.warn(`⚠️ Warning: Failed to sync database to disk:`, syncErr);
                                                } else {
                                                    logger.info(`💾 Database synced to disk successfully`);
                                                }

                                                logger.info(`✅ USER UPGRADED: @${username} (ID: ${user.id}) is now PAID until ${newExpiry.toISOString()}`);
                                                logger.info(`💾 Database transaction committed and synced`);

                                                // Force refresh Telegram bot user cache and database connection
                                                if (this.telegramBot) {
                                                    this.telegramBot.refreshUserCache(user.id);
                                                    this.telegramBot.setDatabaseInstance(db);
                                                }

                                                resolve({
                                                    success: true,
                                                    message: 'Duration added successfully',
                                                    newExpiry: newExpiry.toISOString(),
                                                    user: user,
                                                    userType: 'paid'
                                                });
                                            });
                                        }
                                    });
                                }
                            });
                        });
                    });
                });
            } catch (error) {
                logger.error('Error adding user duration by username:', error);
                return { success: false, error: error.message };
            }
        });

        // Get user status by username for duration management
        ipcMain.handle('get-user-status-by-username', async (event, username) => {
            try {
                const db = database.db();
                if (!db) {
                    return { success: false, error: 'Database not available' };
                }

                return new Promise((resolve) => {
                    db.get(`
                        SELECT id, username, first_name, last_name, user_type,
                               subscription_type, subscription_expires_at, joined_at, last_activity
                        FROM users WHERE username = ?
                    `, [username], (err, user) => {
                        if (err) {
                            resolve({ success: false, error: err.message });
                        } else if (user) {
                            resolve({ success: true, user });
                        } else {
                            resolve({ success: false, error: `User with username '@${username}' not found` });
                        }
                    });
                });
            } catch (error) {
                logger.error('Error getting user status by username:', error);
                return { success: false, error: error.message };
            }
        });

        // Create test user with username for testing duration management
        ipcMain.handle('create-test-user-with-username', async (event) => {
            try {
                const db = database.db();
                if (!db) {
                    return { success: false, error: 'Database not available' };
                }

                const now = new Date().toISOString();
                const testUserId = 999999999;
                const testUsername = 'sytus';

                return new Promise((resolve) => {
                    db.run(`
                        INSERT OR REPLACE INTO users (
                            id, username, first_name, last_name, language_code,
                            is_bot, joined_at, last_activity, is_banned,
                            user_type, subscription_type, subscription_expires_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    `, [
                        testUserId, testUsername, 'Test', 'User', 'en',
                        0, now, now, 0,
                        'free', null, null
                    ], function(err) {
                        if (err) {
                            resolve({ success: false, error: err.message });
                        } else {
                            logger.info(`Created test user with username: @${testUsername} (ID: ${testUserId})`);
                            resolve({
                                success: true,
                                message: 'Test user created successfully',
                                user: {
                                    id: testUserId,
                                    username: testUsername,
                                    first_name: 'Test',
                                    last_name: 'User'
                                }
                            });
                        }
                    });
                });
            } catch (error) {
                logger.error('Error creating test user with username:', error);
                return { success: false, error: error.message };
            }
        });

        // Test user type detection
        ipcMain.handle('test-user-type-detection', async (event, userId) => {
            try {
                if (!this.telegramBot) {
                    return { success: false, error: 'Telegram bot not initialized' };
                }

                const userType = await this.telegramBot.getUserType(userId);

                logger.info(`🔍 USER TYPE TEST: User ${userId} detected as: ${userType}`);

                return {
                    success: true,
                    userType: userType,
                    message: `User ${userId} is detected as: ${userType}`
                };
            } catch (error) {
                logger.error('Error testing user type detection:', error);
                return { success: false, error: error.message };
            }
        });

        // Debug database state
        ipcMain.handle('debug-database-state', async (event, userId) => {
            try {
                const db = database.db();
                if (!db) {
                    return { success: false, error: 'Database not available' };
                }

                return new Promise((resolve) => {
                    db.get(`
                        SELECT id, username, first_name, user_type, subscription_type,
                               subscription_expires_at, joined_at, last_activity
                        FROM users WHERE id = ?
                    `, [userId], (err, user) => {
                        if (err) {
                            resolve({ success: false, error: err.message });
                        } else if (user) {
                            const now = new Date();
                            const expiresAt = user.subscription_expires_at ? new Date(user.subscription_expires_at) : null;

                            logger.info(`🔍 DATABASE STATE for user ${userId}:`);
                            logger.info(`   - Raw data: ${JSON.stringify(user, null, 2)}`);
                            logger.info(`   - Current time: ${now.toISOString()}`);
                            logger.info(`   - Expires at: ${expiresAt ? expiresAt.toISOString() : 'null'}`);
                            logger.info(`   - Is expired: ${expiresAt ? (expiresAt <= now) : 'N/A'}`);

                            resolve({
                                success: true,
                                user: user,
                                currentTime: now.toISOString(),
                                expiresAt: expiresAt ? expiresAt.toISOString() : null,
                                isExpired: expiresAt ? (expiresAt <= now) : null
                            });
                        } else {
                            resolve({ success: false, error: 'User not found' });
                        }
                    });
                });
            } catch (error) {
                logger.error('Error debugging database state:', error);
                return { success: false, error: error.message };
            }
        });

        // Comprehensive user type test
        ipcMain.handle('comprehensive-user-test', async (event, username) => {
            try {
                const db = database.db();
                if (!db) {
                    return { success: false, error: 'Database not available' };
                }

                return new Promise((resolve) => {
                    // First get user by username
                    db.get(`SELECT * FROM users WHERE username = ?`, [username], (err, user) => {
                        if (err) {
                            resolve({ success: false, error: err.message });
                            return;
                        }

                        if (!user) {
                            resolve({ success: false, error: `User @${username} not found` });
                            return;
                        }

                        const now = new Date();
                        const expiresAt = user.subscription_expires_at ? new Date(user.subscription_expires_at) : null;

                        logger.info(`🔍 COMPREHENSIVE TEST for @${username} (ID: ${user.id}):`);
                        logger.info(`   📊 Database Record:`);
                        logger.info(`      - user_type: "${user.user_type}"`);
                        logger.info(`      - subscription_type: "${user.subscription_type}"`);
                        logger.info(`      - subscription_expires_at: "${user.subscription_expires_at}"`);
                        logger.info(`   ⏰ Time Analysis:`);
                        logger.info(`      - Current time: ${now.toISOString()}`);
                        logger.info(`      - Expires at: ${expiresAt ? expiresAt.toISOString() : 'null'}`);
                        logger.info(`      - Time difference: ${expiresAt ? (expiresAt - now) : 'N/A'} ms`);
                        logger.info(`      - Is expired: ${expiresAt ? (expiresAt <= now) : 'N/A'}`);

                        // Test user type detection via Telegram bot
                        if (this.telegramBot) {
                            this.telegramBot.getUserType(user.id).then(detectedType => {
                                logger.info(`   🤖 Telegram Bot Detection: "${detectedType}"`);

                                resolve({
                                    success: true,
                                    user: user,
                                    currentTime: now.toISOString(),
                                    expiresAt: expiresAt ? expiresAt.toISOString() : null,
                                    isExpired: expiresAt ? (expiresAt <= now) : null,
                                    timeDifference: expiresAt ? (expiresAt - now) : null,
                                    telegramBotDetection: detectedType,
                                    expectedType: (user.user_type === 'paid' && expiresAt && expiresAt > now) ? 'paid' : 'free'
                                });
                            }).catch(botError => {
                                logger.error(`❌ Telegram bot detection failed:`, botError);
                                resolve({
                                    success: true,
                                    user: user,
                                    currentTime: now.toISOString(),
                                    expiresAt: expiresAt ? expiresAt.toISOString() : null,
                                    isExpired: expiresAt ? (expiresAt <= now) : null,
                                    timeDifference: expiresAt ? (expiresAt - now) : null,
                                    telegramBotDetection: 'ERROR: ' + botError.message,
                                    expectedType: (user.user_type === 'paid' && expiresAt && expiresAt > now) ? 'paid' : 'free'
                                });
                            });
                        } else {
                            resolve({
                                success: true,
                                user: user,
                                currentTime: now.toISOString(),
                                expiresAt: expiresAt ? expiresAt.toISOString() : null,
                                isExpired: expiresAt ? (expiresAt <= now) : null,
                                timeDifference: expiresAt ? (expiresAt - now) : null,
                                telegramBotDetection: 'Bot not available',
                                expectedType: (user.user_type === 'paid' && expiresAt && expiresAt > now) ? 'paid' : 'free'
                            });
                        }
                    });
                });
            } catch (error) {
                logger.error('Error in comprehensive user test:', error);
                return { success: false, error: error.message };
            }
        });

        // Force database sync to disk
        ipcMain.handle('force-database-sync', async (event) => {
            try {
                const db = database.db();
                if (!db) {
                    return { success: false, error: 'Database not available' };
                }

                return new Promise((resolve) => {
                    db.run('PRAGMA wal_checkpoint(FULL)', (err) => {
                        if (err) {
                            logger.error('❌ Failed to force database sync:', err);
                            resolve({ success: false, error: err.message });
                        } else {
                            logger.info('💾 Database force sync completed successfully');
                            resolve({ success: true, message: 'Database synced to disk' });
                        }
                    });
                });
            } catch (error) {
                logger.error('Error forcing database sync:', error);
                return { success: false, error: error.message };
            }
        });

        // Settings
        ipcMain.handle('get-settings', async (event) => {
            try {
                const settingsPath = path.join(__dirname, 'data', 'settings.json');

                // Default settings
                const defaultSettings = {
                    questionsPerPage: 5,  // Questions per page for multi-page documents
                    imageQuestionsCount: 15,  // Total questions for images
                    questionType: 'MCQ',
                    autoSave: true,
                    notifications: true
                };

                try {
                    // Try to read existing settings
                    const fs = require('fs');
                    if (fs.existsSync(settingsPath)) {
                        const settingsData = fs.readFileSync(settingsPath, 'utf8');
                        const savedSettings = JSON.parse(settingsData);

                        // Merge with defaults to ensure all properties exist
                        const settings = { ...defaultSettings, ...savedSettings };
                        logger.info('Loaded settings from file:', settings);
                        return { success: true, settings };
                    }
                } catch (readError) {
                    logger.warn('Could not read settings file, using defaults:', readError.message);
                }

                // Return defaults if file doesn't exist or can't be read
                logger.info('Using default settings');
                return { success: true, settings: defaultSettings };
            } catch (error) {
                logger.error('Error getting settings:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('save-settings', async (event, newSettings) => {
            try {
                const settingsPath = path.join(__dirname, 'data', 'settings.json');
                const fs = require('fs');

                // Ensure data directory exists
                const dataDir = path.dirname(settingsPath);
                if (!fs.existsSync(dataDir)) {
                    fs.mkdirSync(dataDir, { recursive: true });
                }

                let currentSettings = {};

                // Load existing settings if they exist
                if (fs.existsSync(settingsPath)) {
                    try {
                        const settingsData = fs.readFileSync(settingsPath, 'utf8');
                        currentSettings = JSON.parse(settingsData);
                    } catch (readError) {
                        logger.warn('Could not read existing settings, starting fresh:', readError.message);
                    }
                }

                // Merge new settings with existing ones
                const updatedSettings = { ...currentSettings, ...newSettings };

                // Save to file
                fs.writeFileSync(settingsPath, JSON.stringify(updatedSettings, null, 2), 'utf8');

                logger.info('Settings saved to file:', JSON.stringify(updatedSettings, null, 2));
                return { success: true };
            } catch (error) {
                logger.error('Error saving settings:', error);
                return { success: false, error: error.message };
            }
        });

        // Feedback
        ipcMain.handle('submit-feedback', async (event, feedback) => {
            try {
                // Save feedback using existing feedback service
                const feedbackService = require('./services/feedbackService');
                const feedbackData = {
                    userId: 'desktop-user',
                    username: 'Desktop User',
                    rating: feedback.rating,
                    suggestion: feedback.suggestion,
                    quizType: feedback.quizType || 'Unknown',
                    score: feedback.score || 0
                };

                const saved = await feedbackService.saveFeedback(feedbackData);

                if (saved) {
                    logger.success('Feedback saved successfully');
                    return { success: true };
                } else {
                    throw new Error('Failed to save feedback');
                }
            } catch (error) {
                logger.error('Error submitting feedback:', error);
                return { success: false, error: error.message };
            }
        });

        // Utility functions
        ipcMain.handle('show-notification', async (event, title, body) => {
            try {
                if (Notification.isSupported()) {
                    new Notification({ title, body }).show();
                }
                return { success: true };
            } catch (error) {
                logger.error('Error showing notification:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('open-external', async (event, url) => {
            try {
                await shell.openExternal(url);
                return { success: true };
            } catch (error) {
                logger.error('Error opening external URL:', error);
                return { success: false, error: error.message };
            }
        });
        ipcMain.handle('show-message-box', async (event, options) => {
            try {
                const { dialog, BrowserWindow } = require('electron');
                const focusedWindow = BrowserWindow.getFocusedWindow();
                const result = await dialog.showMessageBox(focusedWindow, options);
                return result;
            } catch (error) {
                logger.error('Error showing message box:', error);
                return { success: false, error: error.message };
            }
        });

        // Dialog functions
        ipcMain.handle('show-save-dialog', async (event, options) => {
            try {
                const { dialog, BrowserWindow } = require('electron');
                const focusedWindow = BrowserWindow.getFocusedWindow();
                const result = await dialog.showSaveDialog(focusedWindow, options);
                return result;
            } catch (error) {
                logger.error('Error showing save dialog:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('show-open-dialog', async (event, options) => {
            try {
                const { dialog, BrowserWindow } = require('electron');
                const focusedWindow = BrowserWindow.getFocusedWindow();
                const result = await dialog.showOpenDialog(focusedWindow, options);
                return result;
            } catch (error) {
                logger.error('Error showing open dialog:', error);
                return { success: false, error: error.message };
            }
        });

        // File save dialog
        ipcMain.handle('save-file', async (event, options) => {
            try {
                const { dialog } = require('electron');
                const result = await dialog.showSaveDialog(options);

                if (result.canceled) {
                    return { success: false, canceled: true };
                } else {
                    return { success: true, filePath: result.filePath };
                }
            } catch (error) {
                logger.error('Error showing save dialog:', error);
                return { success: false, error: error.message };
            }
        });

        // PDF generation and save
        ipcMain.handle('save-pdf', async (event, filePath, htmlContent) => {
            try {
                const fs = require('fs').promises;
                const { BrowserWindow } = require('electron');

                // Create a hidden browser window for PDF generation
                const pdfWindow = new BrowserWindow({
                    show: false,
                    webPreferences: {
                        nodeIntegration: false,
                        contextIsolation: true
                    }
                });

                // Load the HTML content
                await pdfWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(htmlContent)}`);

                // Generate PDF
                const pdfBuffer = await pdfWindow.webContents.printToPDF({
                    format: 'A4',
                    margin: {
                        top: 20,
                        right: 15,
                        bottom: 20,
                        left: 15
                    },
                    printBackground: true,
                    landscape: false
                });

                // Close the window
                pdfWindow.close();

                // Save PDF to file
                await fs.writeFile(filePath, pdfBuffer);

                logger.success(`PDF saved to: ${filePath}`);
                return { success: true };
            } catch (error) {
                logger.error('Error generating PDF:', error);
                return { success: false, error: error.message };
            }
        });

        // Image-based PDF generation
        ipcMain.handle('save-image-as-pdf', async (event, filePath, imageData) => {
            try {
                const PDFLib = require('pdf-lib');
                const fs = require('fs').promises;

                logger.info('Creating PDF from captured image...');

                // Create a new PDF document
                const pdfDoc = await PDFLib.PDFDocument.create();

                // Convert image buffer to Uint8Array
                const imageBytes = new Uint8Array(imageData.imageBuffer);

                // Embed the PNG image
                const image = await pdfDoc.embedPng(imageBytes);
                const imageDims = image.scale(1);

                // Calculate page size to fit the image
                const maxWidth = 595; // A4 width in points minus margins
                const maxHeight = 842; // A4 height in points minus margins

                let pageWidth = imageDims.width;
                let pageHeight = imageDims.height;

                // Scale down if image is too large
                if (pageWidth > maxWidth || pageHeight > maxHeight) {
                    const scaleX = maxWidth / pageWidth;
                    const scaleY = maxHeight / pageHeight;
                    const scale = Math.min(scaleX, scaleY);

                    pageWidth = pageWidth * scale;
                    pageHeight = pageHeight * scale;
                }

                // Add a page with appropriate size
                const page = pdfDoc.addPage([pageWidth + 40, pageHeight + 60]); // Add some padding

                // Draw the image on the page
                page.drawImage(image, {
                    x: 20,
                    y: 20,
                    width: pageWidth,
                    height: pageHeight,
                });

                // Add title if provided
                if (imageData.title) {
                    const font = await pdfDoc.embedFont(PDFLib.StandardFonts.HelveticaBold);
                    page.drawText(imageData.title, {
                        x: 20,
                        y: pageHeight + 35,
                        size: 16,
                        font: font,
                        color: PDFLib.rgb(0.2, 0.2, 0.2),
                    });
                }

                // Set PDF metadata
                pdfDoc.setTitle(imageData.title || 'Mind Map');
                pdfDoc.setCreator('MCQ & TF Question Generator');
                pdfDoc.setProducer('Mind Map Export Tool');
                pdfDoc.setCreationDate(new Date());

                // Save the PDF
                const pdfBytes = await pdfDoc.save();
                await fs.writeFile(filePath, pdfBytes);

                logger.success(`Image-based PDF saved to: ${filePath}`);
                return { success: true };
            } catch (error) {
                logger.error('Error generating image-based PDF:', error);
                return { success: false, error: error.message };
            }
        });

        // Save file content
        ipcMain.handle('save-file-content', async (event, filePath, content) => {
            try {
                const fs = require('fs').promises;

                // Convert ArrayBuffer to Buffer if needed
                let buffer;
                if (content instanceof ArrayBuffer) {
                    buffer = Buffer.from(content);
                } else if (Buffer.isBuffer(content)) {
                    buffer = content;
                } else {
                    buffer = Buffer.from(content);
                }

                await fs.writeFile(filePath, buffer);
                logger.success(`File content saved to: ${filePath}`);
                return { success: true };
            } catch (error) {
                logger.error('Error saving file content:', error);
                return { success: false, error: error.message };
            }
        });





        // Model Management
        ipcMain.handle('add-model', async (event, modelData) => {
            try {
                logger.info(`Adding new model: ${modelData.id}`);

                // Read current models configuration
                const modelsPath = path.join(__dirname, 'config', 'models.json');
                let models = [];

                // Ensure config directory exists
                const configDir = path.dirname(modelsPath);
                if (!fs.existsSync(configDir)) {
                    fs.mkdirSync(configDir, { recursive: true });
                }

                if (fs.existsSync(modelsPath)) {
                    const modelsData = fs.readFileSync(modelsPath, 'utf8');
                    models = JSON.parse(modelsData);
                }

                // Check if model already exists
                const existingModel = models.find(m => m.id === modelData.id);
                if (existingModel) {
                    return { success: false, error: 'Model already exists' };
                }

                // Add new model
                const newModel = {
                    id: modelData.id,
                    name: modelData.name,
                    description: modelData.description || '',
                    added: new Date().toISOString(),
                    custom: true
                };

                models.push(newModel);

                // Save updated models
                fs.writeFileSync(modelsPath, JSON.stringify(models, null, 2));

                logger.info(`Model ${modelData.id} added successfully`);
                return { success: true };

            } catch (error) {
                logger.error('Error adding model:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('remove-model', async (event, modelId) => {
            try {
                logger.info(`Removing model: ${modelId}`);

                // No restrictions - allow removal of ANY model

                // Read current removed models list
                const removedModelsPath = path.join(__dirname, 'config', 'removed-models.json');
                let removedModels = [];

                // Ensure config directory exists
                const configDir = path.dirname(removedModelsPath);
                if (!fs.existsSync(configDir)) {
                    fs.mkdirSync(configDir, { recursive: true });
                }

                if (fs.existsSync(removedModelsPath)) {
                    const removedData = fs.readFileSync(removedModelsPath, 'utf8');
                    removedModels = JSON.parse(removedData);
                }

                // Add model to removed list if not already there
                if (!removedModels.includes(modelId)) {
                    removedModels.push(modelId);
                    fs.writeFileSync(removedModelsPath, JSON.stringify(removedModels, null, 2));
                }

                // Also remove from custom models if it exists there
                const customModelsPath = path.join(__dirname, 'config', 'models.json');
                if (fs.existsSync(customModelsPath)) {
                    const customModelsData = fs.readFileSync(customModelsPath, 'utf8');
                    let customModels = JSON.parse(customModelsData);

                    const modelIndex = customModels.findIndex(m => m.id === modelId);
                    if (modelIndex !== -1) {
                        customModels.splice(modelIndex, 1);
                        fs.writeFileSync(customModelsPath, JSON.stringify(customModels, null, 2));
                    }
                }

                logger.info(`Model ${modelId} removed successfully`);
                return { success: true };

            } catch (error) {
                logger.error('Error removing model:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('get-all-models', async (event) => {
            try {
                // Read custom models first
                const customModelsPath = path.join(__dirname, 'config', 'models.json');
                let customModels = [];

                if (fs.existsSync(customModelsPath)) {
                    const modelsData = fs.readFileSync(customModelsPath, 'utf8');
                    customModels = JSON.parse(modelsData);
                }

                // If custom models exist, use ONLY custom models (user preference)
                if (customModels.length > 0) {
                    logger.info(`Using ${customModels.length} custom models only`);
                    return { success: true, models: customModels };
                }

                // No fallback models - user must add models through the UI
                logger.info('No custom models found - user must add models through the UI');
                return { success: true, models: [] };

            } catch (error) {
                logger.error('Error getting models:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('check-model-availability', async (event, modelId) => {
            try {
                // Simple availability check - just return success for now
                // In a real implementation, you might ping the model API
                return {
                    success: true,
                    available: true,
                    status: 'available',
                    message: 'Model is available'
                };
            } catch (error) {
                logger.error('Error checking model availability:', error);
                return {
                    success: false,
                    available: false,
                    status: 'error',
                    message: error.message
                };
            }
        });

        // Model Testing
        ipcMain.handle('test-model', async (event, modelId, content, questionType, questionCount) => {
            try {
                logger.info(`Testing model: ${modelId}`);

                // Use the existing question generation logic with correct parameter order
                const questions = await apiService.generateQuestionsFromAPI(
                    content,           // text
                    questionType,      // type
                    questionCount,     // count
                    0,                 // retries
                    false,             // isScanned
                    'desktop-user',    // userId
                    'text',            // contentType
                    modelId            // preferredModel - force specific model
                );

                if (questions && questions.length > 0) {
                    logger.info(`Model ${modelId} test successful: ${questions.length} questions generated`);
                    return {
                        success: true,
                        questions: questions,
                        model: modelId
                    };
                } else {
                    logger.warn(`Model ${modelId} test failed: No questions generated`);
                    return {
                        success: false,
                        error: 'No questions generated',
                        model: modelId
                    };
                }

            } catch (error) {
                logger.error(`Error testing model ${modelId}:`, error);
                return {
                    success: false,
                    error: error.message,
                    model: modelId
                };
            }
        });

        // Run Model Simulation
        ipcMain.handle('run-model-simulation', async (event, testParams) => {
            try {
                logger.info('Starting automated model simulation...');

                const { content, questionType, questionCount } = testParams;

                // Get all available models from custom models only
                const customModelsPath = path.join(__dirname, 'config', 'models.json');
                let availableModels = [];

                if (fs.existsSync(customModelsPath)) {
                    const customModelsData = fs.readFileSync(customModelsPath, 'utf8');
                    const customModels = JSON.parse(customModelsData);
                    availableModels = customModels;
                }

                logger.info(`Testing ${availableModels.length} models...`);

                const results = [];

                // Test each model
                for (let i = 0; i < availableModels.length; i++) {
                    const model = availableModels[i];
                    logger.info(`Testing model ${i + 1}/${availableModels.length}: ${model.id}`);

                    const startTime = Date.now();

                    try {
                        // Test the model with correct parameter order
                        const questions = await apiService.generateQuestionsFromAPI(
                            content,           // text
                            questionType,      // type
                            questionCount,     // count
                            0,                 // retries
                            false,             // isScanned
                            'desktop-user',    // userId
                            'text',            // contentType
                            model.id           // preferredModel - force specific model
                        );

                        const endTime = Date.now();
                        const duration = endTime - startTime;

                        if (questions && questions.length > 0) {
                            logger.info(`✓ Model ${model.id} SUCCESS: ${questions.length} questions in ${duration}ms`);
                            results.push({
                                model: model,
                                success: true,
                                duration: duration,
                                questionsGenerated: questions.length,
                                questions: questions
                            });
                        } else {
                            logger.warn(`✗ Model ${model.id} FAILED: No questions generated`);
                            results.push({
                                model: model,
                                success: false,
                                duration: duration,
                                error: 'No questions generated'
                            });
                        }

                    } catch (error) {
                        const endTime = Date.now();
                        const duration = endTime - startTime;

                        logger.error(`✗ Model ${model.id} ERROR: ${error.message}`);
                        results.push({
                            model: model,
                            success: false,
                            duration: duration,
                            error: error.message
                        });
                    }

                    // Small delay between tests
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }

                // Generate summary
                const successCount = results.filter(r => r.success).length;
                const totalCount = results.length;
                const avgDuration = Math.round(
                    results.reduce((sum, r) => sum + r.duration, 0) / results.length
                );

                logger.info(`Simulation complete: ${successCount}/${totalCount} models working, avg ${avgDuration}ms`);

                return {
                    success: true,
                    results: results,
                    summary: {
                        totalModels: totalCount,
                        successfulModels: successCount,
                        failedModels: totalCount - successCount,
                        averageDuration: avgDuration
                    }
                };

            } catch (error) {
                logger.error('Error running model simulation:', error);
                return {
                    success: false,
                    error: error.message
                };
            }
        });

        // True/False Logic Testing
        ipcMain.handle('test-tf-logic', async (event, testQuestions = null) => {
            try {
                logger.info('Testing True/False logic validation...');
                const apiService = require('./services/apiService');
                const result = apiService.testTrueFalseLogic(testQuestions);
                return { success: true, result };
            } catch (error) {
                logger.error('Error testing TF logic:', error);
                return { success: false, error: error.message };
            }
        });

        // Rate limit management
        ipcMain.handle('get-rate-limited-models', async (event) => {
            try {
                const rateLimitedModels = apiService.getRateLimitedModels();
                return { success: true, models: rateLimitedModels };
            } catch (error) {
                logger.error('Error getting rate-limited models:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('clear-model-rate-limit', async (event, modelId) => {
            try {
                apiService.clearModelRateLimit(modelId);
                logger.info(`Cleared rate limit for model: ${modelId}`);
                return { success: true };
            } catch (error) {
                logger.error('Error clearing model rate limit:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('clear-all-rate-limits', async (event) => {
            try {
                const clearedModels = apiService.clearAllRateLimits();
                logger.info(`Cleared rate limits for ${clearedModels.length} models`);
                return { success: true, clearedModels: clearedModels };
            } catch (error) {
                logger.error('Error clearing all rate limits:', error);
                return { success: false, error: error.message };
            }
        });

        // Gemini CLI Integration
        ipcMain.handle('gemini-check-auth', async (event) => {
            try {
                const status = await this.geminiService.checkAuthStatus();
                return {
                    success: true,
                    status: status,
                    isAuthenticated: this.geminiService.isAuthenticated,
                    userInfo: this.geminiService.userInfo || null
                };
            } catch (error) {
                logger.error('Error checking Gemini auth status:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('gemini-start-auth', async (event) => {
            try {
                const result = await this.geminiService.startAuthentication();
                return result;
            } catch (error) {
                logger.error('Error starting Gemini authentication:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('gemini-generate-questions', async (event, content, options = {}) => {
            try {
                logger.info(`AI Agent: Generating ${options.count} ${options.type} questions...`);

                // Use the actual Gemini CLI for question generation with rate limit failover
                const result = await this.callGeminiCLIWithFailover(content, options, 'questions');

                if (result.success && result.questions) {
                    logger.success(`AI Agent: Generated ${result.questions.length} questions`);
                    return { success: true, questions: result.questions };
                } else {
                    throw new Error(result.error || 'Failed to generate questions with AI Agent');
                }
            } catch (error) {
                logger.error('AI Agent Error Details:', error);
                logger.error('AI Agent Error Stack:', error.stack);
                logger.error('Error generating questions with AI Agent:', error);
                return { success: false, error: error.message };
            }
        });

        // AI Agent Mind Map Generation
        ipcMain.handle('gemini-generate-mindmap', async (event, content, options = {}) => {
            try {
                logger.info('AI Agent: Generating mind map...');
                logger.debug('Content length:', content?.length || 0);

                // Use the actual Gemini CLI for mind map generation with rate limit failover
                const result = await this.callGeminiCLIWithFailover(content, options, 'mindmap');

                if (result.success && result.mindMap) {
                    logger.success(`AI Agent: Generated mind map with title: ${result.mindMap?.title || 'Unknown'}`);
                    return { success: true, mindMap: result.mindMap };
                } else {
                    throw new Error(result.error || 'Failed to generate mind map with AI Agent');
                }
            } catch (error) {
                logger.error('Error generating mind map with AI Agent:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('gemini-get-auth-status', async (event) => {
            try {
                const status = this.geminiService.getAuthStatus();
                return { success: true, ...status };
            } catch (error) {
                logger.error('Error getting Gemini auth status:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('gemini-stop-process', async (event) => {
            try {
                this.geminiService.stopCurrentProcess();
                return { success: true };
            } catch (error) {
                logger.error('Error stopping Gemini process:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('gemini-poll-auth', async (event) => {
            try {
                const result = await this.geminiService.pollForAuthCompletion();
                return result;
            } catch (error) {
                logger.error('Error polling Gemini auth:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('gemini-setup-apikey', async (event, apiKey) => {
            try {
                const result = await this.geminiService.setupWithApiKey(apiKey);
                return result;
            } catch (error) {
                logger.error('Error setting up Gemini API key:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('gemini-start-terminal-auth', async (event) => {
            try {
                const result = await this.geminiService.startTerminalAuthentication();
                return result;
            } catch (error) {
                logger.error('Error starting terminal authentication:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('gemini-check-auth-status', async (event) => {
            try {
                const result = await this.geminiService.checkAuthenticationStatus();
                return result;
            } catch (error) {
                logger.error('Error checking authentication status:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('gemini-open-browser-auth', async (event) => {
            try {
                const result = await this.geminiService.openBrowserAuth();
                return result;
            } catch (error) {
                logger.error('Error opening browser auth:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('gemini-sign-out', async (event) => {
            try {
                const result = await this.geminiService.signOut();
                return result;
            } catch (error) {
                logger.error('Error signing out from Gemini:', error);
                return { success: false, error: error.message };
            }
        });

        // Store terminal process for later cleanup
        this.authTerminalProcess = null;
        this.authFileWatcher = null;
        this.mainWindow = null;

        ipcMain.handle('open-terminal-auth', async (event) => {
            try {
                const { spawn, exec } = require('child_process');
                const fs = require('fs');

                logger.info('Starting local AI Agent CLI authentication...');

                // Kill any existing auth process
                if (this.authTerminalProcess && !this.authTerminalProcess.killed) {
                    try {
                        this.authTerminalProcess.kill();
                    } catch (e) {
                        // Ignore errors
                    }
                }

                // Use local Gemini CLI with project-contained settings
                const localGeminiDir = path.join(__dirname, '..', '.gemini-local');
                const localGeminiCLI = path.join(__dirname, '..', 'gemini-cli-main', 'gemini-cli-main', 'bundle', 'gemini.js');

                // Ensure local settings exist
                if (!fs.existsSync(localGeminiDir)) {
                    fs.mkdirSync(localGeminiDir, { recursive: true });
                    const settings = { selectedAuthType: "oauth" };
                    fs.writeFileSync(path.join(localGeminiDir, 'settings.json'), JSON.stringify(settings, null, 2));
                }

                // Use local Gemini CLI bundle (fully portable)
                const headlessCommand = `powershell -WindowStyle Hidden -Command "Start-Process cmd -ArgumentList '/c', 'set GEMINI_CONFIG_DIR=${localGeminiDir.replace(/\\/g, '\\\\')} && echo Starting local Gemini CLI authentication... && node \\"${localGeminiCLI.replace(/\\/g, '\\\\')}\\"' -WindowStyle Hidden"`;

                exec(headlessCommand, (error, stdout, stderr) => {
                    if (error) {
                        logger.debug('Headless mode failed, trying minimized fallback:', error.message);

                        // Fallback to minimized window if headless fails
                        const fallbackCommand = `start /min cmd /k "set GEMINI_CONFIG_DIR=${localGeminiDir.replace(/\\/g, '\\\\')} && echo Starting Gemini CLI authentication... && node \\"${localGeminiCLI.replace(/\\/g, '\\\\')}\\""`;

                        exec(fallbackCommand, (fallbackError, fallbackStdout, fallbackStderr) => {
                            if (fallbackError) {
                                logger.debug('Fallback error:', fallbackError.message);
                            } else {
                                logger.debug('Fallback: Minimized terminal opened for AI Agent authentication');
                            }
                        });
                    } else {
                        logger.success('Headless AI Agent authentication started successfully');
                    }
                });

                // Store a reference for cleanup (we'll track by process name instead)
                this.authTerminalProcess = { pid: Date.now(), killed: false };

                // Start monitoring the OAuth credentials file for changes
                this.startAuthFileWatcher();

                return { success: true, pid: this.authTerminalProcess.pid };
            } catch (error) {
                logger.error('Error starting Gemini CLI auth:', error);
                return { success: false, error: error.message };
            }
        });

        // Add file watcher method
        this.startAuthFileWatcher = () => {
            try {
                const fs = require('fs');
                const path = require('path');
                const os = require('os');

                // Watch system directory where Gemini CLI writes, then copy to local
                const credentialsPath = path.join(os.homedir(), '.gemini', 'oauth_creds.json');
                const localGeminiDir = path.join(__dirname, '..', '.gemini-local');
                const credentialsDir = path.dirname(credentialsPath);

                logger.debug('Starting file watcher for credentials');

                // Stop any existing watcher
                if (this.authFileWatcher) {
                    this.authFileWatcher.close();
                }

                // Watch the .gemini directory for file changes
                this.authFileWatcher = fs.watch(credentialsDir, (eventType, filename) => {
                    if (filename === 'oauth_creds.json' && eventType === 'change') {
                        logger.success('OAuth credentials file changed! Authentication successful!');

                        // Verify the file exists and has content
                        setTimeout(() => {
                            try {
                                if (fs.existsSync(credentialsPath)) {
                                    const stats = fs.statSync(credentialsPath);
                                    if (stats.size > 0) {
                                        logger.success('OAuth credentials file confirmed with data');

                                        // Read and display user info
                                        try {
                                            const credentialsData = fs.readFileSync(credentialsPath, 'utf8');
                                            const credentials = JSON.parse(credentialsData);
                                            logger.debug('User authentication info validated successfully');
                                        } catch (parseError) {
                                            logger.debug('Could not parse credentials file:', parseError.message);
                                        }

                                        // Also check for user info file
                                        const userInfoPath = path.join(os.homedir(), '.gemini', 'google_account_id');
                                        if (fs.existsSync(userInfoPath)) {
                                            try {
                                                const userInfo = fs.readFileSync(userInfoPath, 'utf8').trim();
                                                logger.debug('Google Account ID found');
                                            } catch (userError) {
                                                logger.debug('Could not read user info file:', userError.message);
                                            }
                                        } else {
                                            logger.debug('No Google account ID file found');
                                        }

                                        // Copy credentials to local project directory
                                        const systemCredentialsPath = path.join(os.homedir(), '.gemini', 'oauth_creds.json');
                                        const systemUserInfoPath = path.join(os.homedir(), '.gemini', 'google_account_id');
                                        const localCredentialsPath = path.join(localGeminiDir, 'oauth_creds.json');
                                        const localUserInfoPath = path.join(localGeminiDir, 'google_account_id');

                                        try {
                                            // Copy credentials file to local directory
                                            if (fs.existsSync(systemCredentialsPath)) {
                                                fs.copyFileSync(systemCredentialsPath, localCredentialsPath);
                                                console.log('📋 Copied credentials to local directory');
                                            }

                                            // Copy user info file to local directory
                                            if (fs.existsSync(systemUserInfoPath)) {
                                                fs.copyFileSync(systemUserInfoPath, localUserInfoPath);
                                                console.log('📋 Copied user info to local directory');
                                            }
                                        } catch (copyError) {
                                            console.log('⚠️ Could not copy files to local directory:', copyError.message);
                                        }

                                        // Stop watching
                                        this.authFileWatcher.close();
                                        this.authFileWatcher = null;

                                        // Auto-save the authenticated account
                                        try {
                                            const saved = this.accountManager.autoSaveCurrentAccount();
                                            if (saved) {
                                                console.log('💾 Account automatically saved to multi-account storage');
                                            } else {
                                                console.log('⚠️ Could not auto-save account');
                                            }
                                        } catch (autoSaveError) {
                                            console.log('⚠️ Error auto-saving account:', autoSaveError.message);
                                        }

                                        // Notify the renderer process
                                        if (this.mainWindow) {
                                            console.log('📡 Sending auth success event to renderer');
                                            this.mainWindow.webContents.send('gemini-auth-success');
                                        }

                                        // Close windows immediately and aggressively
                                        this.closeAuthWindowsImmediate();

                                        // Also run regular closing as backup
                                        setTimeout(() => {
                                            this.closeAuthWindows();
                                        }, 1000);
                                    }
                                }
                            } catch (error) {
                                console.log('Error verifying credentials file:', error.message);
                            }
                        }, 1000); // Wait 1 second to ensure file is fully written
                    }
                });

                console.log('🔍 File watcher started successfully');

            } catch (error) {
                console.error('Error starting file watcher:', error);
            }
        };

        // Add immediate method to close authentication windows
        this.closeAuthWindowsImmediate = () => {
            console.log('⚡ IMMEDIATE: Closing authentication windows...');
            const { exec } = require('child_process');

            // Immediate aggressive closing - target recent PowerShell windows
            const immediateScript = `
                $ErrorActionPreference = "SilentlyContinue"

                # Close ALL PowerShell windows opened in last 5 minutes (very aggressive)
                Get-Process powershell | Where-Object {
                    try {
                        $timeDiff = (Get-Date) - $_.StartTime
                        $timeDiff.TotalMinutes -lt 5
                    } catch { $false }
                } | ForEach-Object {
                    Write-Host "IMMEDIATE: Killing PowerShell PID $($_.Id)"
                    try { $_.Kill() } catch { }
                }

                # Also close any cmd windows with authentication content
                Get-Process cmd | Where-Object {
                    try {
                        $_.MainWindowTitle -match "gemini|Gemini|GEMINI|Starting|authentication"
                    } catch { $false }
                } | ForEach-Object {
                    Write-Host "IMMEDIATE: Killing cmd PID $($_.Id)"
                    try { $_.Kill() } catch { }
                }
            `;

            exec(`powershell -Command "${immediateScript}"`, (error, stdout) => {
                console.log('⚡ Immediate close result:', stdout || 'No output');
            });
        };

        // Add method to close authentication windows
        this.closeAuthWindows = async () => {
            try {
                console.log('🗙 Automatically closing authentication windows...');

                const { exec } = require('child_process');

                // Method 1: Close browser windows more aggressively
                const closeBrowserScript = `
                    $ErrorActionPreference = "SilentlyContinue"

                    # Close all browser windows with Google/OAuth content
                    $browsers = Get-Process | Where-Object {$_.ProcessName -match "chrome|msedge|firefox|brave|iexplore"}
                    foreach ($browser in $browsers) {
                        try {
                            $title = $browser.MainWindowTitle
                            Write-Host "Checking browser: $($browser.ProcessName) - $title"

                            # More aggressive matching
                            if ($title -match "accounts.google.com|oauth|sign|google|gemini|authentication|Sign in|Gmail") {
                                Write-Host "Closing browser: $title"
                                $browser.CloseMainWindow()
                                Start-Sleep -Milliseconds 500
                                if (!$browser.HasExited) {
                                    Write-Host "Force killing browser process"
                                    $browser.Kill()
                                }
                            }
                        } catch {
                            Write-Host "Error processing browser: $_"
                        }
                    }

                    # Also send Alt+F4 to close active window
                    Add-Type -AssemblyName System.Windows.Forms
                    [System.Windows.Forms.SendKeys]::SendWait('%{F4}')
                    Write-Host "Sent Alt+F4 to close active window"
                `;

                exec(`powershell -Command "${closeBrowserScript}"`, (error, stdout) => {
                    console.log('🌐 Browser close result:', stdout);
                    if (error) console.log('🌐 Browser close error:', error.message);
                });

                // Method 2: Close Gemini authentication terminals (cmd AND PowerShell windows)
                const closeTerminalScript = `
                    $ErrorActionPreference = "SilentlyContinue"

                    # Close both cmd and PowerShell windows that are for Gemini authentication
                    $terminals = Get-Process | Where-Object {$_.ProcessName -match "cmd|powershell|pwsh"}
                    foreach ($terminal in $terminals) {
                        try {
                            $title = $terminal.MainWindowTitle
                            Write-Host "Checking terminal: $($terminal.ProcessName) - $title"

                            # Close terminals with Gemini-related content (but NOT the main app terminal)
                            if ($title -match "gemini|Gemini|authentication|Starting Gemini|GEMINI" -and $title -notmatch "MCQ|npm|electron") {
                                Write-Host "Closing Gemini terminal: $($terminal.ProcessName) - $title"
                                $terminal.CloseMainWindow()
                                Start-Sleep -Milliseconds 500
                                if (!$terminal.HasExited) {
                                    Write-Host "Force killing Gemini terminal process: $($terminal.Id)"
                                    $terminal.Kill()
                                }
                            } elseif ($title -match "Windows PowerShell" -and $terminal.ProcessName -eq "powershell") {
                                # Check if this PowerShell window was opened for Gemini (by checking if it's recent)
                                $startTime = $terminal.StartTime
                                $timeDiff = (Get-Date) - $startTime
                                if ($timeDiff.TotalMinutes -lt 5) {
                                    Write-Host "Closing recent PowerShell window (likely Gemini auth): $title"
                                    $terminal.CloseMainWindow()
                                    Start-Sleep -Milliseconds 500
                                    if (!$terminal.HasExited) {
                                        $terminal.Kill()
                                    }
                                }
                            } else {
                                Write-Host "Skipping terminal (not Gemini-related): $($terminal.ProcessName) - $title"
                            }
                        } catch {
                            Write-Host "Error processing terminal: $_"
                        }
                    }
                `;

                exec(`powershell -Command "${closeTerminalScript}"`, (error, stdout) => {
                    console.log('💻 Terminal close result:', stdout);
                    if (error) console.log('💻 Terminal close error:', error.message);
                });

                // Method 3: Aggressive window closing - target all authentication-related windows
                setTimeout(() => {
                    // Send Ctrl+W to close browser tabs
                    exec('powershell -Command "Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.SendKeys]::SendWait(\'^w\')"', (error) => {
                        if (!error) console.log('🌐 Sent Ctrl+W to close browser tab');
                    });

                    // More aggressive PowerShell closing - target by content and timing
                    const aggressiveCloseScript = `
                        $ErrorActionPreference = "SilentlyContinue"

                        # Method 1: Close PowerShell windows opened in last 10 minutes
                        Get-Process powershell | Where-Object {
                            try {
                                $timeDiff = (Get-Date) - $_.StartTime
                                $timeDiff.TotalMinutes -lt 10
                            } catch { $false }
                        } | ForEach-Object {
                            try {
                                Write-Host "Closing recent PowerShell: PID $($_.Id) - $($_.MainWindowTitle)"
                                $_.CloseMainWindow()
                                Start-Sleep -Milliseconds 200
                                if (!$_.HasExited) {
                                    Write-Host "Force killing PowerShell PID: $($_.Id)"
                                    $_.Kill()
                                }
                            } catch {
                                Write-Host "Error closing PowerShell: $_"
                            }
                        }

                        # Method 2: Close cmd windows with Gemini content
                        Get-Process cmd | Where-Object {
                            try {
                                $title = $_.MainWindowTitle
                                $title -match "gemini|Gemini|GEMINI|Starting|authentication" -and $title -notmatch "MCQ|npm|electron"
                            } catch { $false }
                        } | ForEach-Object {
                            try {
                                Write-Host "Closing Gemini cmd: PID $($_.Id) - $($_.MainWindowTitle)"
                                $_.CloseMainWindow()
                                Start-Sleep -Milliseconds 200
                                if (!$_.HasExited) { $_.Kill() }
                            } catch {
                                Write-Host "Error closing cmd: $_"
                            }
                        }
                    `;

                    exec(`powershell -Command "${aggressiveCloseScript}"`, (error, stdout) => {
                        console.log('💻 Aggressive window close result:', stdout || 'No output');
                        if (error) console.log('⚠️ Window close error:', error.message);
                    });
                }, 2000);

                console.log('✅ Enhanced window closing commands sent');

            } catch (error) {
                console.error('Error closing auth windows:', error);
            }
        };

        ipcMain.handle('close-browser-windows', async (event) => {
            try {
                const { exec } = require('child_process');

                console.log('🌐 Attempting to close browser windows...');

                // Method 1: More aggressive - close all browser windows with Google/OAuth content
                const closeScript = `
                    $ErrorActionPreference = "SilentlyContinue"

                    # Get all browser processes
                    $browsers = Get-Process | Where-Object {$_.ProcessName -match "chrome|msedge|firefox|brave|iexplore"}

                    foreach ($browser in $browsers) {
                        try {
                            $title = $browser.MainWindowTitle
                            Write-Host "Checking browser: $title"

                            # Close if title contains Google/OAuth related terms
                            if ($title -match "accounts.google.com|oauth|sign|google|gemini|authentication") {
                                Write-Host "Closing browser window: $title"
                                $browser.CloseMainWindow()
                                Start-Sleep -Milliseconds 300
                                if (!$browser.HasExited) {
                                    $browser.Kill()
                                    Write-Host "Force killed browser process"
                                }
                            }
                        } catch {
                            Write-Host "Error processing browser: $_"
                        }
                    }
                `;

                exec(`powershell -Command "${closeScript}"`, (error, stdout, stderr) => {
                    console.log('🌐 Browser close output:', stdout);
                    if (error) {
                        console.log('🌐 Browser close error (may be normal):', error.message);
                    }
                });

                // Method 2: Send Alt+F4 to close active window after delay
                setTimeout(() => {
                    exec('powershell -Command "Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.SendKeys]::SendWait(\'%{F4}\')"', (error) => {
                        if (error) {
                            console.log('🌐 Alt+F4 error:', error.message);
                        } else {
                            console.log('🌐 Sent Alt+F4 to close active window');
                        }
                    });
                }, 1500);

                return { success: true };
            } catch (error) {
                logger.error('Error closing browser windows:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('close-terminal-windows', async (event) => {
            try {
                const { exec } = require('child_process');

                console.log('💻 Attempting to close terminal windows...');

                // Method 1: Close the specific auth terminal if we have reference
                if (this.authTerminalProcess && !this.authTerminalProcess.killed) {
                    try {
                        console.log('💻 Closing auth terminal with PID:', this.authTerminalProcess.pid);
                        process.kill(this.authTerminalProcess.pid, 'SIGTERM');
                        this.authTerminalProcess = null;
                        console.log('💻 Successfully closed auth terminal');
                    } catch (error) {
                        console.log('💻 Error closing specific terminal:', error.message);
                    }
                }

                // Method 2: More aggressive terminal closing
                const closeTerminalScript = `
                    $ErrorActionPreference = "SilentlyContinue"

                    # Close all cmd windows with gemini-related content
                    $cmdProcesses = Get-Process | Where-Object {$_.ProcessName -eq "cmd"}
                    foreach ($proc in $cmdProcesses) {
                        try {
                            $title = $proc.MainWindowTitle
                            Write-Host "Checking terminal: $title"

                            if ($title -match "gemini|Gemini|authentication|Starting|CLI") {
                                Write-Host "Closing terminal: $title"
                                $proc.CloseMainWindow()
                                Start-Sleep -Milliseconds 300
                                if (!$proc.HasExited) {
                                    $proc.Kill()
                                    Write-Host "Force killed terminal process"
                                }
                            }
                        } catch {
                            Write-Host "Error processing terminal: $_"
                        }
                    }

                    # Also close PowerShell windows that might be related
                    $psProcesses = Get-Process | Where-Object {$_.ProcessName -eq "powershell"}
                    foreach ($proc in $psProcesses) {
                        try {
                            $title = $proc.MainWindowTitle
                            if ($title -match "gemini|authentication") {
                                Write-Host "Closing PowerShell: $title"
                                $proc.CloseMainWindow()
                                Start-Sleep -Milliseconds 300
                                if (!$proc.HasExited) {
                                    $proc.Kill()
                                }
                            }
                        } catch {}
                    }
                `;

                exec(`powershell -Command "${closeTerminalScript}"`, (error, stdout, stderr) => {
                    console.log('💻 Terminal close output:', stdout);
                    if (error) {
                        console.log('💻 Terminal close error (may be normal):', error.message);
                    }
                });

                // Method 3: Fallback - close all cmd windows after delay (more aggressive)
                setTimeout(() => {
                    exec('taskkill /F /IM cmd.exe', (error, stdout, stderr) => {
                        if (error) {
                            console.log('💻 Taskkill error (expected if no cmd windows):', error.message);
                        } else {
                            console.log('💻 Closed all remaining cmd windows');
                        }
                    });
                }, 3000);

                return { success: true };
            } catch (error) {
                logger.error('Error closing terminal windows:', error);
                return { success: false, error: error.message };
            }
        });

        // PDF Operations
        ipcMain.handle('merge-pdfs', async (event, outputPath, pdfBuffers, options = {}) => {
            try {
                const PDFLib = require('pdf-lib');
                const fs = require('fs').promises;

                logger.info(`Merging ${pdfBuffers.length} PDF files with options:`, {
                    quality: options.quality || 'balanced',
                    maintainBookmarks: options.maintainBookmarks || false,
                    addTableOfContents: options.addTableOfContents || false,
                    addPageNumbers: options.addPageNumbers || false
                });

                // Create a new PDF document
                const mergedPdf = await PDFLib.PDFDocument.create();
                let totalPages = 0;
                let tocPages = [];

                // Process each PDF buffer
                for (let i = 0; i < pdfBuffers.length; i++) {
                    const pdfBuffer = pdfBuffers[i];

                    try {
                        const pdf = await PDFLib.PDFDocument.load(pdfBuffer);
                        const pageCount = pdf.getPageCount();
                        const startPage = totalPages + 1; // +1 because page numbers are 1-based
                        totalPages += pageCount;

                        // Store TOC information
                        if (options.addTableOfContents && options.sourceFileNames && options.sourceFileNames[i]) {
                            tocPages.push({
                                fileName: options.sourceFileNames[i],
                                startPage: startPage,
                                endPage: totalPages,
                                pageCount: pageCount
                            });
                        }

                        // Copy all pages from this PDF
                        const pageIndices = Array.from({ length: pageCount }, (_, index) => index);
                        const copiedPages = await mergedPdf.copyPages(pdf, pageIndices);

                        // Add copied pages to merged PDF
                        copiedPages.forEach((page) => mergedPdf.addPage(page));

                        // Copy bookmarks if requested
                        if (options.maintainBookmarks) {
                            try {
                                // Note: pdf-lib doesn't have direct bookmark copying,
                                // but we can preserve document structure
                                const title = pdf.getTitle();
                                if (title && i === 0) {
                                    mergedPdf.setTitle(title);
                                }
                            } catch (bookmarkError) {
                                logger.warn(`Could not copy bookmarks from PDF ${i + 1}: ${bookmarkError.message}`);
                            }
                        }

                        logger.info(`Merged PDF ${i + 1}/${pdfBuffers.length} (${pageCount} pages)`);
                    } catch (pdfError) {
                        logger.error(`Error processing PDF ${i + 1}: ${pdfError.message}`);
                        throw new Error(`Failed to process PDF file ${i + 1}: ${pdfError.message}`);
                    }
                }

                // Add Table of Contents if requested
                if (options.addTableOfContents && tocPages.length > 0) {
                    try {
                        const tocPage = mergedPdf.insertPage(0); // Insert at the beginning
                        const { width, height } = tocPage.getSize();

                        // Add title
                        tocPage.drawText('Table of Contents', {
                            x: 50,
                            y: height - 80,
                            size: 20,
                            color: PDFLib.rgb(0, 0, 0)
                        });

                        // Add TOC entries
                        let yPosition = height - 120;
                        tocPages.forEach((entry, index) => {
                            const text = `${entry.fileName} ........................ Pages ${entry.startPage}-${entry.endPage}`;
                            tocPage.drawText(text, {
                                x: 50,
                                y: yPosition,
                                size: 12,
                                color: PDFLib.rgb(0, 0, 0)
                            });
                            yPosition -= 25;
                        });

                        totalPages += 1; // Account for TOC page
                        logger.info('Added Table of Contents page');
                    } catch (tocError) {
                        logger.warn('Could not create Table of Contents:', tocError.message);
                    }
                }

                // Add page numbers if requested
                if (options.addPageNumbers) {
                    try {
                        const pages = mergedPdf.getPages();
                        pages.forEach((page, index) => {
                            const { width, height } = page.getSize();
                            const pageNumber = index + 1;
                            page.drawText(`${pageNumber}`, {
                                x: width - 50,
                                y: 30,
                                size: 10,
                                color: PDFLib.rgb(0.5, 0.5, 0.5)
                            });
                        });
                        logger.info('Added page numbers to merged PDF');
                    } catch (pageNumberError) {
                        logger.warn('Could not add page numbers:', pageNumberError.message);
                    }
                }

                // Set metadata for merged PDF
                mergedPdf.setCreator('PDF Editor');
                mergedPdf.setProducer('PDF Editor - Merge Tool');
                mergedPdf.setCreationDate(new Date());
                mergedPdf.setModificationDate(new Date());

                // Save the merged PDF
                const mergedPdfBytes = await mergedPdf.save();
                await fs.writeFile(outputPath, mergedPdfBytes);

                logger.success(`PDFs merged successfully to: ${outputPath} (${totalPages} total pages)`);
                return { success: true, totalPages: totalPages };
            } catch (error) {
                logger.error('Error merging PDFs:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('validate-pdf', async (event, pdfBuffer) => {
            try {
                const PDFLib = require('pdf-lib');

                // Try to load the PDF to validate it
                const pdf = await PDFLib.PDFDocument.load(pdfBuffer);
                const pageCount = pdf.getPageCount();

                // Basic validation checks
                if (pageCount === 0) {
                    return false;
                }

                return true;
            } catch (error) {
                logger.warn('PDF validation failed:', error.message);
                return false;
            }
        });

        ipcMain.handle('get-pdf-page-count', async (event, pdfBuffer) => {
            try {
                const PDFLib = require('pdf-lib');
                const pdf = await PDFLib.PDFDocument.load(pdfBuffer);
                return pdf.getPageCount();
            } catch (error) {
                logger.error('Error getting PDF page count:', error);
                return 0;
            }
        });



        ipcMain.handle('delete-pdf-pages', async (event, pdfBuffer, outputPath, pagesToDelete) => {
            try {
                const PDFLib = require('pdf-lib');
                const fs = require('fs').promises;

                logger.info(`Deleting pages from PDF`);

                // Load the PDF from buffer
                const pdf = await PDFLib.PDFDocument.load(pdfBuffer);
                const totalPages = pdf.getPageCount();

                logger.info(`PDF has ${totalPages} pages, deleting: ${pagesToDelete}`);

                // Parse pages to delete
                const deleteIndices = this.parsePageNumbers(pagesToDelete, totalPages);

                // Create new PDF with remaining pages
                const newPdf = await PDFLib.PDFDocument.create();
                const pagesToKeep = [];

                for (let i = 0; i < totalPages; i++) {
                    if (!deleteIndices.includes(i)) {
                        pagesToKeep.push(i);
                    }
                }

                if (pagesToKeep.length === 0) {
                    throw new Error('Cannot delete all pages from PDF');
                }

                const copiedPages = await newPdf.copyPages(pdf, pagesToKeep);
                copiedPages.forEach((page) => newPdf.addPage(page));

                // Save the new PDF
                const newPdfBytes = await newPdf.save();
                await fs.writeFile(outputPath, newPdfBytes);

                logger.success(`Deleted ${deleteIndices.length} pages, saved to: ${outputPath}`);
                return { success: true, deletedPages: deleteIndices.length, remainingPages: pagesToKeep.length };
            } catch (error) {
                logger.error('Error deleting PDF pages:', error);
                return { success: false, error: error.message };
            }
        });

        // Split PDF
        ipcMain.handle('split-pdf', async (event, pdfBuffer, outputPath, options = {}) => {
            try {
                const PDFLib = require('pdf-lib');
                const fs = require('fs').promises;
                const path = require('path');

                logger.info(`Splitting PDF with method: ${options.method}`);

                // Load the PDF
                const pdf = await PDFLib.PDFDocument.load(pdfBuffer);
                const pageCount = pdf.getPageCount();

                logger.info(`PDF has ${pageCount} pages`);

                // Extract directory and base name from the output path
                const outputDir = path.dirname(outputPath);
                const outputBaseName = path.basename(outputPath, '.pdf');

                logger.info(`Output directory: ${outputDir}`);
                logger.info(`Base filename: ${outputBaseName}`);

                let splitRanges = [];
                let fileCount = 0;
                let firstFilePath = null;

                // Determine split ranges based on method
                switch (options.method) {
                    case 'pages':
                        splitRanges = this.parsePageRanges(options.pageRanges, pageCount);
                        break;
                    case 'every':
                        splitRanges = this.generateEveryNPagesRanges(pageCount, options.everyNPages);
                        break;
                    case 'extract':
                        splitRanges = this.parsePageRanges(options.extractPages, pageCount);
                        break;
                    default:
                        throw new Error('Invalid split method');
                }

                if (splitRanges.length === 0) {
                    throw new Error('No valid page ranges specified');
                }

                // Create output directory if it doesn't exist
                await fs.mkdir(outputDir, { recursive: true });

                // Create split PDFs
                for (let i = 0; i < splitRanges.length; i++) {
                    const range = splitRanges[i];
                    const newPdf = await PDFLib.PDFDocument.create();

                    // Copy pages in the range
                    const pageIndices = [];
                    for (let pageNum = range.start; pageNum <= range.end; pageNum++) {
                        if (pageNum >= 1 && pageNum <= pageCount) {
                            pageIndices.push(pageNum - 1); // Convert to 0-based index
                        }
                    }

                    if (pageIndices.length > 0) {
                        const copiedPages = await newPdf.copyPages(pdf, pageIndices);
                        copiedPages.forEach(page => newPdf.addPage(page));

                        // Set metadata
                        newPdf.setCreator('PDF Editor');
                        newPdf.setProducer('PDF Editor - Split Tool');
                        newPdf.setCreationDate(new Date());

                        // Generate filename using user-provided base name
                        let filename;
                        if (options.method === 'extract') {
                            filename = `${outputBaseName}_pages_${range.start}-${range.end}.pdf`;
                        } else {
                            filename = `${outputBaseName}_part_${i + 1}_pages_${range.start}-${range.end}.pdf`;
                        }

                        const filePath = path.join(outputDir, filename);

                        // Save the split PDF
                        const pdfBytes = await newPdf.save();
                        await fs.writeFile(filePath, pdfBytes);

                        // Store the first file path for opening
                        if (fileCount === 0) {
                            firstFilePath = filePath;
                        }

                        fileCount++;
                        logger.info(`Created: ${filename} (pages ${range.start}-${range.end})`);
                    }
                }

                logger.success(`PDF split successfully into ${fileCount} files in: ${outputDir}`);
                return {
                    success: true,
                    fileCount: fileCount,
                    outputDir: outputDir,
                    firstFilePath: firstFilePath
                };
            } catch (error) {
                logger.error('Error splitting PDF:', error);
                return { success: false, error: error.message };
            }
        });

        // Helper methods for PDF operations
        this.parsePageRanges = (rangeString, maxPages) => {
            if (!rangeString || !rangeString.trim()) {
                return [];
            }

            const ranges = [];
            const parts = rangeString.split(',').map(s => s.trim());

            for (const part of parts) {
                if (part.includes('-')) {
                    // Range like "1-5"
                    const [start, end] = part.split('-').map(s => parseInt(s.trim()));
                    if (!isNaN(start) && !isNaN(end) && start >= 1 && end <= maxPages && start <= end) {
                        ranges.push({ start, end });
                    }
                } else {
                    // Single page like "3"
                    const page = parseInt(part);
                    if (!isNaN(page) && page >= 1 && page <= maxPages) {
                        ranges.push({ start: page, end: page });
                    }
                }
            }

            return ranges;
        };

        this.generateEveryNPagesRanges = (totalPages, pagesPerFile) => {
            const ranges = [];
            let start = 1;

            while (start <= totalPages) {
                const end = Math.min(start + pagesPerFile - 1, totalPages);
                ranges.push({ start, end });
                start = end + 1;
            }

            return ranges;
        };

        // Create Text PDF
        ipcMain.handle('create-text-pdf', async (event, outputPath, textContent, options = {}) => {
            try {
                const PDFLib = require('pdf-lib');
                const fs = require('fs').promises;

                logger.info(`Creating text PDF with options:`, {
                    pageSize: options.pageSize || 'a4',
                    fontSize: options.fontSize || 12,
                    fontFamily: options.fontFamily || 'Arial',
                    margins: options.margins || 'medium',
                    lineSpacing: options.lineSpacing || '1.15'
                });

                // Create a new PDF document
                const pdf = await PDFLib.PDFDocument.create();

                // Set page size
                let pageSize;
                switch (options.pageSize) {
                    case 'letter':
                        pageSize = PDFLib.PageSizes.Letter;
                        break;
                    case 'legal':
                        pageSize = PDFLib.PageSizes.Legal;
                        break;
                    case 'a4':
                    default:
                        pageSize = PDFLib.PageSizes.A4;
                        break;
                }

                // Set margins
                let margins;
                switch (options.margins) {
                    case 'small':
                        margins = { top: 36, bottom: 36, left: 36, right: 36 }; // 0.5 inch
                        break;
                    case 'large':
                        margins = { top: 108, bottom: 108, left: 108, right: 108 }; // 1.5 inch
                        break;
                    case 'medium':
                    default:
                        margins = { top: 72, bottom: 72, left: 72, right: 72 }; // 1 inch
                        break;
                }

                // Get font
                let font;
                try {
                    switch (options.fontFamily) {
                        case 'Times New Roman':
                            font = await pdf.embedFont(PDFLib.StandardFonts.TimesRoman);
                            break;
                        case 'Helvetica':
                            font = await pdf.embedFont(PDFLib.StandardFonts.Helvetica);
                            break;
                        case 'Courier New':
                            font = await pdf.embedFont(PDFLib.StandardFonts.Courier);
                            break;
                        case 'Arial':
                        default:
                            font = await pdf.embedFont(PDFLib.StandardFonts.Helvetica);
                            break;
                    }
                } catch (fontError) {
                    logger.warn(`Could not embed font ${options.fontFamily}, using default`);
                    font = await pdf.embedFont(PDFLib.StandardFonts.Helvetica);
                }

                const fontSize = options.fontSize || 12;
                const lineSpacing = parseFloat(options.lineSpacing) || 1.15;
                const lineHeight = fontSize * lineSpacing;

                // Calculate text area dimensions
                const textWidth = pageSize[0] - margins.left - margins.right;
                const textHeight = pageSize[1] - margins.top - margins.bottom;

                // Split text into lines that fit the page width
                const lines = this.wrapText(textContent, font, fontSize, textWidth);

                // Calculate how many lines fit per page
                const linesPerPage = Math.floor(textHeight / lineHeight);

                // Create pages and add text
                let currentLine = 0;
                while (currentLine < lines.length) {
                    const page = pdf.addPage(pageSize);

                    let y = pageSize[1] - margins.top - fontSize;
                    let linesOnThisPage = 0;

                    while (currentLine < lines.length && linesOnThisPage < linesPerPage) {
                        page.drawText(lines[currentLine], {
                            x: margins.left,
                            y: y,
                            size: fontSize,
                            font: font,
                            color: PDFLib.rgb(0, 0, 0)
                        });

                        y -= lineHeight;
                        currentLine++;
                        linesOnThisPage++;
                    }
                }

                // Set metadata
                pdf.setTitle('Text Document');
                pdf.setCreator('PDF Editor');
                pdf.setProducer('PDF Editor - Text to PDF Tool');
                pdf.setCreationDate(new Date());
                pdf.setModificationDate(new Date());

                // Save the PDF
                const pdfBytes = await pdf.save();
                await fs.writeFile(outputPath, pdfBytes);

                logger.success(`Text PDF created successfully: ${outputPath}`);
                return { success: true };
            } catch (error) {
                logger.error('Error creating text PDF:', error);
                return { success: false, error: error.message };
            }
        });

        // Helper method to wrap text
        this.wrapText = (text, font, fontSize, maxWidth) => {
            const lines = [];
            const paragraphs = text.split('\n');

            for (const paragraph of paragraphs) {
                if (paragraph.trim() === '') {
                    lines.push(''); // Empty line for paragraph breaks
                    continue;
                }

                const words = paragraph.split(' ');
                let currentLine = '';

                for (const word of words) {
                    const testLine = currentLine ? `${currentLine} ${word}` : word;
                    const testWidth = font.widthOfTextAtSize(testLine, fontSize);

                    if (testWidth <= maxWidth) {
                        currentLine = testLine;
                    } else {
                        if (currentLine) {
                            lines.push(currentLine);
                            currentLine = word;
                        } else {
                            // Word is too long, break it
                            lines.push(word);
                        }
                    }
                }

                if (currentLine) {
                    lines.push(currentLine);
                }
            }

            return lines;
        };

        // Account Management Handlers
        ipcMain.handle('account-get-saved-accounts', async (event) => {
            try {
                const accounts = this.accountManager.getAccountsForDropdown();
                return { success: true, accounts };
            } catch (error) {
                logger.error('Error getting saved accounts:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('account-switch-to', async (event, email) => {
            try {
                const success = this.accountManager.switchToAccount(email);
                if (success) {
                    // Auto-save the switched account as current
                    this.accountManager.autoSaveCurrentAccount();
                    return { success: true };
                } else {
                    return { success: false, error: 'Failed to switch account' };
                }
            } catch (error) {
                logger.error('Error switching account:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('account-auto-save-current', async (event) => {
            try {
                const success = this.accountManager.autoSaveCurrentAccount();
                return { success };
            } catch (error) {
                logger.error('Error auto-saving current account:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('account-switch-on-rate-limit', async (event) => {
            try {
                const result = await this.accountManager.switchToNextAccountOnRateLimit();
                return result;
            } catch (error) {
                logger.error('Error switching account on rate limit:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('account-remove', async (event, email) => {
            try {
                const success = this.accountManager.removeAccount(email);
                return { success };
            } catch (error) {
                logger.error('Error removing account:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('account-get-current', async (event) => {
            try {
                const currentAccount = this.accountManager.getCurrentAccountInfo();
                return { success: true, account: currentAccount };
            } catch (error) {
                logger.error('Error getting current account:', error);
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('account-refresh', async (event) => {
            try {
                // Force refresh the account info by re-saving current account
                const refreshed = this.accountManager.autoSaveCurrentAccount();
                const currentAccount = this.accountManager.getCurrentAccountInfo();
                return { success: true, refreshed, account: currentAccount };
            } catch (error) {
                logger.error('Error refreshing account:', error);
                return { success: false, error: error.message };
            }
        });
    }



    // Initialize database tables for desktop app
    initializeDesktopTables() {
        try {
            const db = database.getDatabase();

            if (!db) {
                logger.warn('Database not available, skipping desktop table initialization');
                return;
            }

            // Create quiz sessions table if it doesn't exist
            db.exec(`
                CREATE TABLE IF NOT EXISTS quiz_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    question_type TEXT NOT NULL,
                    score_correct INTEGER NOT NULL,
                    score_total INTEGER NOT NULL,
                    duration INTEGER NOT NULL,
                    answers TEXT,
                    questions TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            `);

            // Initialize API key in database if it doesn't exist
            this.initializeApiKeyInDatabase();

            logger.success('Desktop database tables initialized');
        } catch (error) {
            logger.error('Error initializing desktop tables:', error.message);
            // Don't throw error, just log it
        }
    }

    // Helper methods for PDF operations
    parsePageRanges(rangeString, totalPages) {
        const ranges = [];
        const parts = rangeString.split(',').map(part => part.trim());

        for (const part of parts) {
            if (part.includes('-')) {
                const [start, end] = part.split('-').map(num => parseInt(num.trim()));
                if (start >= 1 && end <= totalPages && start <= end) {
                    ranges.push({ start, end });
                }
            } else {
                const pageNum = parseInt(part);
                if (pageNum >= 1 && pageNum <= totalPages) {
                    ranges.push({ start: pageNum, end: pageNum });
                }
            }
        }

        return ranges;
    }

    parsePageNumbers(pageString, totalPages) {
        const pageIndices = [];
        const parts = pageString.split(',').map(part => part.trim());

        for (const part of parts) {
            if (part.includes('-')) {
                const [start, end] = part.split('-').map(num => parseInt(num.trim()));
                if (start >= 1 && end <= totalPages && start <= end) {
                    for (let i = start; i <= end; i++) {
                        pageIndices.push(i - 1); // Convert to 0-based index
                    }
                }
            } else {
                const pageNum = parseInt(part);
                if (pageNum >= 1 && pageNum <= totalPages) {
                    pageIndices.push(pageNum - 1); // Convert to 0-based index
                }
            }
        }

        return [...new Set(pageIndices)]; // Remove duplicates
    }

    // Initialize API key in database from environment
    async initializeApiKeyInDatabase() {
        try {
            const currentApiKey = process.env.API_KEY;

            if (!currentApiKey) {
                logger.warn('No API key found in environment variables');
                return;
            }

            // Check if we already have this key in the database
            const apiService = require('./services/apiService');
            const db = database.getDatabase();

            if (!db) {
                logger.warn('Database not available for API key initialization');
                return;
            }

            // Check if any API keys exist in database
            db.get('SELECT COUNT(*) as count FROM api_keys WHERE is_active = 1', [], async (err, row) => {
                if (err) {
                    logger.error('Error checking API keys in database:', err);
                    return;
                }

                if (row.count === 0) {
                    // No active API keys in database, add the current environment key
                    try {
                        await apiService.addApiKey(currentApiKey);
                        logger.info('Initialized database with current environment API key');
                    } catch (addError) {
                        logger.warn('Could not add environment API key to database:', addError.message);
                    }
                } else {
                    logger.info('Database already has active API keys');
                }
            });

        } catch (error) {
            logger.error('Error initializing API key in database:', error.message);
        }
    }

    setMainWindow(mainWindow) {
        this.mainWindow = mainWindow;
        console.log('🔗 Main window reference set in IPC handlers');
    }

    /**
     * Call Gemini API directly using Node.js integration
     */
    async callGeminiCLIForQuestions(content, options = {}) {
        try {
            const type = options.type || 'MCQ';
            const count = options.count; // Use the exact count from UI, no fallback cap

            // Debug info removed for cleaner logs

            // Validate count
            if (!count || count < 1) {
                throw new Error(`Invalid question count: ${count}. Please set a valid number in your settings.`);
            }

            logger.info(`AI Agent: Generating exactly ${count} questions as requested (no cap applied)`);

            // Use your existing prompt creation logic
            const systemMessage = this.createSystemMessage(type, count);
            const userMessage = this.createUserMessage(content, type, count);

            // Combine system and user messages
            const prompt = `${systemMessage}\n\n${userMessage}\n\nIMPORTANT: Return ONLY a valid JSON array. Do not include any other text, explanations, or formatting. Start with [ and end with ].`;

            logger.debug('Calling AI Agent API directly with prompt length:', prompt.length);

            // Use the Gemini API directly via Node.js
            const result = await this.callGeminiAPIDirectly(prompt);

            if (result.success && result.response) {
                try {
                    // Parse the response using your existing logic
                    const questions = this.parseGeminiQuestionResponse(result.response, type);
                    logger.success(`AI Agent: Generated ${questions.length} questions via direct API`);
                    return { success: true, questions };
                } catch (parseError) {
                    logger.error('Failed to parse AI Agent API response:', parseError);
                    return { success: false, error: `Failed to parse response: ${parseError.message}` };
                }
            } else {
                throw new Error(result.error || 'Failed to get response from Gemini API');
            }

        } catch (error) {
            logger.error('AI Agent API Error Details:', error);
            logger.error('AI Agent API Error Stack:', error.stack);
            return { success: false, error: error.message };
        }
    }

    /**
     * Call Gemini API directly for mind map generation using your existing prompts
     */
    async callGeminiCLIForMindMap(content, options = {}) {
        try {
            // Use your existing mind map prompt creation logic
            const systemMessage = this.createMindMapSystemMessage();
            const userMessage = this.createMindMapUserMessage(content);

            // Combine system and user messages
            const prompt = `${systemMessage}\n\n${userMessage}\n\nIMPORTANT: Return ONLY a valid JSON object. Do not include any other text, explanations, or formatting. Start with { and end with }.`;

            logger.debug('Calling AI Agent API directly for mind map with prompt length:', prompt.length);

            // Use the Gemini API directly via Node.js
            const result = await this.callGeminiAPIDirectly(prompt);

            if (result.success && result.response) {
                try {
                    // Parse the response using your existing logic
                    const mindMap = this.parseGeminiMindMapResponse(result.response);
                    logger.success(`AI Agent: Generated mind map via direct API: ${mindMap?.title || 'Unknown'}`);
                    return { success: true, mindMap };
                } catch (parseError) {
                    logger.error('Failed to parse AI Agent API mind map response:', parseError);
                    return { success: false, error: `Failed to parse mind map response: ${parseError.message}` };
                }
            } else {
                throw new Error(result.error || 'Failed to get mind map response from Gemini API');
            }

        } catch (error) {
            logger.error('AI Agent API Mind Map Error Details:', error);
            logger.error('AI Agent API Mind Map Error Stack:', error.stack);
            return { success: false, error: error.message };
        }
    }

    /**
     * Call Gemini CLI with automatic rate limit failover and OpenRouter fallback
     */
    async callGeminiCLIWithFailover(content, options, type = 'questions', maxAccountRetries = 3) {
        let attemptCount = 0;
        let lastError = null;

        while (attemptCount < maxAccountRetries) {
            try {
                logger.debug(`Attempt ${attemptCount + 1}/${maxAccountRetries} for AI Agent CLI ${type}`);

                // Try the current account
                let result;
                if (type === 'questions') {
                    result = await this.callGeminiCLIForQuestions(content, options);
                } else if (type === 'mindmap') {
                    result = await this.callGeminiCLIForMindMap(content, options);
                } else {
                    result = await this.callGeminiAPIDirectly(content);
                }

                if (result.success) {
                    logger.success(`AI Agent CLI ${type} successful on attempt ${attemptCount + 1}`);
                    return result;
                }

                // Check if it's a rate limit, server overload, timeout, or process failure
                const isRateLimit = result.error && (
                    result.error.includes('429') ||
                    result.error.includes('502') ||
                    result.error.includes('rate limit') ||
                    result.error.includes('quota exceeded') ||
                    result.error.includes('Too Many Requests') ||
                    result.error.includes('Server Error') ||
                    result.error.includes('Bad Gateway') ||
                    result.error.includes('timeout') ||
                    result.error.includes('Gemini CLI timeout') ||
                    result.error.includes('failed with code null') ||
                    result.error.includes('process killed') ||
                    result.error.includes('SIGTERM') ||
                    result.error.includes('returned no output') ||
                    result.error.includes('no output')
                );

                if (isRateLimit && attemptCount < maxAccountRetries - 1) {
                    console.log(`🚨 Failover condition detected: ${result.error}`);
                    console.log(`🔄 Attempting account switch (attempt ${attemptCount + 1}/${maxAccountRetries})`);

                    // Try to switch to next account
                    const switchResult = await this.accountManager.switchToNextAccountOnRateLimit();

                    if (switchResult.success) {
                        console.log(`✅ Successfully switched to account: ${switchResult.newAccount} (${switchResult.accountName})`);
                        console.log(`🔄 Retrying ${type} generation with new account...`);
                        attemptCount++;
                        lastError = result.error;
                        continue; // Retry with new account
                    } else {
                        console.log(`❌ Account switch failed: ${switchResult.reason}`);
                        break; // No more accounts to try
                    }
                } else {
                    // Not a failover condition or no more retries
                    if (!isRateLimit) {
                        console.log(`❌ Error not eligible for account switching: ${result.error}`);
                    } else {
                        console.log(`❌ Max retry attempts reached (${maxAccountRetries})`);
                    }
                    lastError = result.error;
                    break;
                }

            } catch (error) {
                console.error(`❌ Gemini CLI attempt ${attemptCount + 1} failed:`, error.message);
                lastError = error.message;

                // Check if it's a rate limit, server overload, timeout, or process failure in the exception
                const isRateLimit = error.message && (
                    error.message.includes('429') ||
                    error.message.includes('502') ||
                    error.message.includes('rate limit') ||
                    error.message.includes('quota exceeded') ||
                    error.message.includes('Too Many Requests') ||
                    error.message.includes('Server Error') ||
                    error.message.includes('Bad Gateway') ||
                    error.message.includes('timeout') ||
                    error.message.includes('Gemini CLI timeout') ||
                    error.message.includes('failed with code null') ||
                    error.message.includes('process killed') ||
                    error.message.includes('SIGTERM') ||
                    error.message.includes('returned no output') ||
                    error.message.includes('no output')
                );

                if (isRateLimit && attemptCount < maxAccountRetries - 1) {
                    console.log(`🚨 Rate limit exception detected: ${error.message}`);

                    // Try to switch to next account
                    const switchResult = await this.accountManager.switchToNextAccountOnRateLimit();

                    if (switchResult.success) {
                        console.log(`🔄 Switched to account: ${switchResult.newAccount} (${switchResult.accountName})`);
                        attemptCount++;
                        continue; // Retry with new account
                    } else {
                        console.log(`❌ Account switch failed: ${switchResult.reason}`);
                        break; // No more accounts to try
                    }
                } else {
                    break; // Not a rate limit error or no more retries
                }
            }
        }

        // All Gemini accounts failed, return error for OpenRouter fallback
        console.log(`❌ All Gemini CLI attempts failed. Last error: ${lastError}`);
        return {
            success: false,
            error: lastError || 'All Gemini accounts exhausted',
            allAccountsExhausted: true
        };
    }

    /**
     * Call Gemini using your existing gemini CLI executable with stdin input to avoid command line length limits
     */
    async callGeminiAPIDirectly(prompt) {
        try {
            logger.info('Using your existing AI Agent CLI executable with stdin input...');

            const { spawn } = require('child_process');

            logger.info('Calling AI Agent CLI...');

            return new Promise((resolve, reject) => {
                // Use the local Gemini CLI wrapper for project-contained setup
                // Pass prompt via stdin since command line has length limits
                const process = spawn('cmd', ['/c', 'gemini-local.cmd'], {
                    stdio: ['pipe', 'pipe', 'pipe'],
                    timeout: 200000, // 200 second timeout for complex prompts
                    windowsHide: true // Hide the command window
                });

                let output = '';
                let errorOutput = '';

                // Write the prompt to stdin and close it
                process.stdin.write(prompt + '\n');
                process.stdin.end();

                process.stdout.on('data', (data) => {
                    const chunk = data.toString();
                    output += chunk;
                    // Log progress for long responses
                    if (output.length % 1000 === 0) {
                        console.log('📝 Receiving response... length:', output.length);
                    }
                });

                process.stderr.on('data', (data) => {
                    errorOutput += data.toString();
                });

                process.on('close', (code) => {
                    // Only log if there are issues
                    if (code !== 0) {
                        console.log('⚠️ Gemini CLI completed with code:', code);
                    }
                    if (errorOutput.length > 0 && !errorOutput.includes('DeprecationWarning')) {
                        console.log('⚠️ Gemini CLI error:', errorOutput.substring(0, 200));
                    }

                    // Clean output by removing deprecation warnings and markdown code blocks
                    let cleanOutput = output.replace(/\(node:\d+\) \[DEP0040\] DeprecationWarning:.*?\n/g, '').trim();

                    // Remove markdown code blocks if present
                    if (cleanOutput.startsWith('```json') && cleanOutput.endsWith('```')) {
                        cleanOutput = cleanOutput.replace(/^```json\s*/, '').replace(/\s*```$/, '').trim();
                        console.log('🧹 Removed markdown code blocks from response');
                    } else if (cleanOutput.startsWith('```') && cleanOutput.endsWith('```')) {
                        cleanOutput = cleanOutput.replace(/^```[a-zA-Z]*\s*/, '').replace(/\s*```$/, '').trim();
                        console.log('🧹 Removed generic markdown code blocks from response');
                    }

                    // Minimal logging for successful responses

                    // Check for authentication errors in stderr
                    const hasAuthError = errorOutput.includes('Please set an Auth method') ||
                                       errorOutput.includes('GEMINI_API_KEY') ||
                                       errorOutput.includes('authentication required') ||
                                       errorOutput.includes('not authenticated');

                    if (code === 0) {
                        if (cleanOutput.length > 0) {
                            console.log('✅ Received response from Gemini CLI');
                            resolve({ success: true, response: cleanOutput });
                        } else if (!hasAuthError) {
                            // Exit code 0 but no output - might be a prompt issue
                            console.log('⚠️ Gemini CLI succeeded but returned no output');
                            reject(new Error('Gemini CLI returned no output. The prompt might be too short or invalid.'));
                        } else {
                            console.error('❌ Authentication error detected');
                            reject(new Error('Gemini CLI authentication required. Please sign in through AI Settings.'));
                        }
                    } else {
                        console.error('❌ Gemini CLI failed with code:', code);
                        console.error('❌ Error output:', errorOutput.substring(0, 500));

                        if (hasAuthError) {
                            reject(new Error('Gemini CLI authentication required. Please sign in through AI Settings.'));
                        } else {
                            reject(new Error(`Gemini CLI failed with code ${code}: ${errorOutput || 'No error output'}`));
                        }
                    }
                });

                process.on('error', (error) => {
                    console.error('❌ Failed to start Gemini CLI process:', error);
                    reject(new Error(`Failed to start Gemini CLI: ${error.message}`));
                });

                // Set timeout with proper cleanup
                const timeoutId = setTimeout(() => {
                    console.log('⏰ Gemini CLI timeout after 200 seconds');
                    try {
                        const isWindows = require('os').platform() === 'win32';
                        if (isWindows) {
                            process.kill('SIGKILL'); // Force kill on Windows
                        } else {
                            process.kill('SIGTERM');
                            // Fallback to SIGKILL after 2 seconds
                            setTimeout(() => {
                                if (!process.killed) {
                                    process.kill('SIGKILL');
                                }
                            }, 2000);
                        }
                    } catch (killError) {
                        console.error('Error killing process:', killError.message);
                    }
                    reject(new Error('Gemini CLI timeout after 200 seconds'));
                }, 200000);

                // Clear timeout when process exits
                process.on('exit', () => clearTimeout(timeoutId));
            });

        } catch (error) {
            console.error('🚨 Gemini CLI Integration Error:', error);
            console.error('🚨 Error Stack:', error.stack);

            // Provide more specific error messages
            if (error.message.includes('ENOENT')) {
                return { success: false, error: 'Gemini CLI not found. Please ensure it is installed and in your PATH.' };
            }

            if (error.message.includes('authentication') || error.message.includes('401')) {
                return { success: false, error: 'Authentication failed. Please ensure you are logged in to Google via the Gemini CLI.' };
            }

            return { success: false, error: error.message };
        }
    }

    /**
     * Refresh Gemini OAuth token
     */
    async refreshGeminiOAuthToken() {
        try {
            const fs = require('fs');
            const path = require('path');
            const os = require('os');
            const https = require('https');

            const oauthCredsPath = path.join(os.homedir(), '.gemini', 'oauth_creds.json');
            const oauthCreds = JSON.parse(fs.readFileSync(oauthCredsPath, 'utf8'));

            if (!oauthCreds.refresh_token) {
                throw new Error('No refresh token available');
            }

            // Refresh the token using Google's OAuth2 endpoint
            const postData = new URLSearchParams({
                client_id: oauthCreds.client_id,
                client_secret: oauthCreds.client_secret,
                refresh_token: oauthCreds.refresh_token,
                grant_type: 'refresh_token'
            }).toString();

            const options = {
                hostname: 'oauth2.googleapis.com',
                port: 443,
                path: '/token',
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'Content-Length': Buffer.byteLength(postData)
                }
            };

            return new Promise((resolve, reject) => {
                const req = https.request(options, (res) => {
                    let data = '';
                    res.on('data', (chunk) => {
                        data += chunk;
                    });
                    res.on('end', () => {
                        try {
                            const response = JSON.parse(data);
                            if (response.access_token) {
                                // Update the credentials file
                                oauthCreds.access_token = response.access_token;
                                if (response.refresh_token) {
                                    oauthCreds.refresh_token = response.refresh_token;
                                }
                                fs.writeFileSync(oauthCredsPath, JSON.stringify(oauthCreds, null, 2));
                                console.log('✅ OAuth token refreshed successfully');
                                resolve();
                            } else {
                                reject(new Error('Failed to refresh token: ' + data));
                            }
                        } catch (error) {
                            reject(new Error('Failed to parse refresh response: ' + error.message));
                        }
                    });
                });

                req.on('error', (error) => {
                    reject(error);
                });

                req.write(postData);
                req.end();
            });

        } catch (error) {
            throw new Error(`Failed to refresh OAuth token: ${error.message}`);
        }
    }

    /**
     * Find Gemini CLI executable path (kept for fallback)
     */
    async findGeminiCLIPath() {
        const { spawn } = require('child_process');
        const os = require('os');
        const path = require('path');

        // Try different possible paths for Gemini CLI
        const possiblePaths = [
            'gemini',  // If in PATH
            path.join(os.homedir(), 'AppData', 'Roaming', 'npm', 'gemini.ps1'),  // Windows npm PowerShell script
            path.join(os.homedir(), 'AppData', 'Roaming', 'npm', 'gemini.cmd'),  // Windows npm batch file
            'npx @google/generative-ai-cli',  // Via npx
            path.join(os.homedir(), '.npm', 'bin', 'gemini'),  // Local npm install
        ];

        for (const geminiPath of possiblePaths) {
            try {
                console.log('🔍 Testing Gemini CLI path:', geminiPath);

                // Test if this path works with a simple prompt
                const result = await new Promise((resolve) => {
                    let spawnCmd, spawnArgs;

                    if (geminiPath.endsWith('.ps1')) {
                        // PowerShell script with execution policy bypass
                        spawnCmd = 'powershell';
                        spawnArgs = ['-ExecutionPolicy', 'Bypass', '-File', geminiPath, '--prompt', 'Generate a simple true/false question about science.'];
                    } else {
                        // Regular executable
                        spawnCmd = geminiPath;
                        spawnArgs = ['--prompt', 'Generate a simple true/false question about science.'];
                    }

                    const testProcess = spawn(spawnCmd, spawnArgs, {
                        stdio: ['pipe', 'pipe', 'pipe'],
                        timeout: 20000
                    });

                    let hasOutput = false;
                    let outputData = '';

                    testProcess.stdout.on('data', (data) => {
                        hasOutput = true;
                        outputData += data.toString();
                        console.log('✅ Gemini CLI responded:', data.toString().substring(0, 100));
                    });

                    testProcess.stderr.on('data', (data) => {
                        console.log('⚠️ Gemini CLI stderr:', data.toString().substring(0, 100));
                    });

                    testProcess.on('close', (code) => {
                        console.log('🔍 Test completed for', geminiPath, 'with code:', code, 'hasOutput:', hasOutput);
                        // If we got output or exit code 0, consider it working
                        resolve((hasOutput || code === 0) ? geminiPath : null);
                    });

                    testProcess.on('error', (error) => {
                        console.log('❌ Error testing path:', geminiPath, error.message);
                        resolve(null);
                    });

                    setTimeout(() => {
                        console.log('⏰ Timeout for path:', geminiPath);
                        testProcess.kill();
                        resolve(null);
                    }, 20000);
                });

                if (result) {
                    console.log('✅ Found working Gemini CLI at:', result);
                    return result;
                }
            } catch (error) {
                console.log('❌ Exception testing path:', geminiPath, error.message);
                // Continue to next path
                continue;
            }
        }

        return null;
    }

    /**
     * Create system message for questions (reusing your existing logic)
     */
    createSystemMessage(type, count) {
        if (type === 'MCQ') {
            return 'You are an expert educator specialized in creating multiple-choice questions. Your output must follow the exact format specified.';
        } else {
            const trueCount = Math.ceil(count / 2);
            const falseCount = count - trueCount;

            return `You are an expert educator specialized in creating perfectly balanced true/false questions.

CRITICAL BALANCE REQUIREMENT: Generate EXACTLY ${trueCount} TRUE and ${falseCount} FALSE questions.
Total must equal ${count} questions. Count carefully: ${trueCount} + ${falseCount} = ${count}

EXPLANATION RULES:
- TRUE answers: No explanation needed (omit explanation field entirely)
- FALSE answers: Must include explanation field explaining why it's false

Your output must be valid JSON array format only.`;
        }
    }

    /**
     * Create user message for questions (reusing your existing logic)
     */
    createUserMessage(text, type, count) {
        if (type === 'MCQ') {
            return `You are a professional question generator. Create ${count} multiple-choice questions based ONLY on the provided content.

🎯 EXACT REQUIREMENTS:
- Generate EXACTLY ${count} questions total
- Each question must have exactly 4 options (A, B, C, D)
- One correct answer and three plausible distractors
- All questions must be answerable from the provided content
- Focus on key concepts, facts, and important details
- Avoid questions about document structure or formatting
- Make distractors plausible but clearly incorrect
- Ensure questions test understanding, not just memorization

📋 QUESTION CREATION RULES:
1. Read the content thoroughly
2. Identify key concepts, facts, and relationships
3. Create questions that test understanding of these concepts
4. Make sure each question has one clearly correct answer
5. Create three distractors that are plausible but wrong
6. Avoid overly obvious or trick questions
7. Focus on educational value

🔀 ANSWER DISTRIBUTION REQUIREMENTS:
- Distribute correct answers evenly across A, B, C, D options
- Aim for roughly equal distribution: 25% A, 25% B, 25% C, 25% D
- Randomize the question order naturally
- Avoid patterns like A-A-A or B-B-B sequences
- Make sure each letter (A, B, C, D) appears as correct answer multiple times
- Place correct answers in different positions throughout the set

REQUIRED JSON STRUCTURE:
[
  {
    "question": "Question text",
    "options": ["Option A", "Option B", "Option C", "Option D"],
    "answer": "A",
    "explanation": "Brief explanation of the correct answer"
  }
]

Content: ${text.substring(0, 12000)}`;
        } else {
            // Calculate how many true and false questions to generate for balance
            const trueCount = Math.ceil(count / 2);
            const falseCount = count - trueCount;

            return `You are a professional question generator. Create ${count} true/false questions based ONLY on the provided content.

🎯 EXACT REQUIREMENTS:
- Generate EXACTLY ${count} questions total
- EXACTLY ${trueCount} questions must have "answer": true
- EXACTLY ${falseCount} questions must have "answer": false
- Count carefully: ${trueCount} + ${falseCount} = ${count}

📋 QUESTION CREATION RULES:
TRUE questions: Create statements that are completely accurate according to the content
FALSE questions: Create statements that contradict or misstate what the content says

🔍 EXPLANATION REQUIREMENTS:
- TRUE answers: Do NOT include explanation field (omit it entirely)
- FALSE answers: MUST include explanation field explaining why the statement is false

REQUIRED JSON STRUCTURE:
[
  {
    "question": "Statement to evaluate",
    "answer": true
  },
  {
    "question": "Statement to evaluate",
    "answer": false,
    "explanation": "Explanation of why this is false"
  }
]

When creating questions:
1. Make sure each question tests understanding of concepts in the content
2. Avoid questions that are too obvious
3. Use specific details from the content
4. BALANCE: ${trueCount} true, ${falseCount} false questions required
5. FALSE questions: Create incorrect statements (wrong classifications, swapped concepts) and provide explanations
6. TRUE questions: Use accurate facts from the content (no explanations needed)
7. Only include explanation field for FALSE answers
8. Focus on content, not document structure

VERIFY: Count ${trueCount} true + ${falseCount} false = ${count} total before submitting.

Content: ${text.substring(0, 12000)}`;
        }
    }

    /**
     * Create system message for mind map (reusing your existing logic)
     */
    createMindMapSystemMessage() {
        return `You are an expert educational content analyzer and professional mind map designer specializing in creating comprehensive, visually optimized study mind maps. You have complete control over the design and structure decisions.

TASK: Analyze the provided content and intelligently design the optimal mind map structure. You decide:
- How many main topics to create (4-12 based on content complexity)
- The depth of hierarchical structure (2-4 levels based on content detail)
- The level of detail for each node (comprehensive for complex topics, concise for simple ones)
- The organization strategy (chronological, categorical, importance-based, etc.)

OUTPUT FORMAT: Return ONLY a valid JSON object with this exact structure:
{
  "title": "Precise, professional title reflecting the main subject",
  "description": "Comprehensive description of the content and learning objectives",
  "layout": {
    "type": "radial|hierarchical|network|timeline",
    "reasoning": "Why you chose this layout for this specific content",
    "complexity": "simple|moderate|complex",
    "focus": "overview|detailed|comprehensive"
  },
  "nodes": [
    {
      "id": "unique_id",
      "label": "Professional Topic Name",
      "description": "Comprehensive description with key facts, examples, and context",
      "level": 1,
      "importance": "high|medium|low",
      "category": "concept|process|fact|principle|application",
      "children": [
        {
          "id": "unique_child_id",
          "label": "Detailed Subtopic Name",
          "description": "In-depth description with specific details, examples, formulas, or procedures",
          "level": 2,
          "importance": "high|medium|low",
          "category": "concept|process|fact|principle|application",
          "children": []
        }
      ]
    }
  ]
}

PROFESSIONAL REQUIREMENTS:
- Create publication-quality content suitable for academic/professional use
- Extract ALL critical information - be comprehensive, not minimal
- Use precise, professional terminology and language
- Include specific facts, data, examples, procedures, and formulas where relevant
- Create logical information hierarchy based on importance and relationships
- Ensure each description is detailed enough to be educational on its own
- Organize for maximum learning effectiveness and retention
- Adapt complexity and depth to match the source material's sophistication
- Choose the optimal number of main topics (4-12) based on content richness
- Create 2-4 levels of hierarchy as needed for complete information coverage`;
    }

    /**
     * Create user message for mind map (reusing your existing logic)
     */
    createMindMapUserMessage(text) {
        return `Analyze this educational content and design the optimal professional mind map. Make intelligent decisions about structure, depth, and organization.

DESIGN DECISIONS TO MAKE:
1. LAYOUT STRATEGY: Choose the best layout type (radial/hierarchical/network/timeline) based on content nature
2. INFORMATION DEPTH: Determine appropriate detail level - comprehensive for complex topics, focused for overviews
3. TOPIC ORGANIZATION: Decide optimal number of main topics (4-12) based on content richness and complexity
4. HIERARCHICAL STRUCTURE: Create 2-4 levels of depth as needed for complete information coverage
5. PROFESSIONAL QUALITY: Ensure content is suitable for academic, business, or professional presentation

CONTENT ANALYSIS REQUIREMENTS:
- Extract ALL critical information, concepts, processes, and relationships
- Include specific facts, data, examples, procedures, formulas, and key details
- Use precise, professional terminology appropriate to the subject matter
- Create educational descriptions that teach the concept, not just label it
- Organize information for maximum learning effectiveness and knowledge retention
- Ensure comprehensive coverage - don't leave out important information
- Adapt complexity to match the sophistication of the source material

CONTENT TO ANALYZE:
${text.substring(0, 15000)}

PROFESSIONAL FOCUS AREAS:
- Core concepts and theoretical foundations
- Practical applications and real-world examples
- Step-by-step processes and methodologies
- Classifications, categories, and taxonomies
- Quantitative data, statistics, and measurements
- Cause-and-effect relationships and dependencies
- Best practices, guidelines, and standards
- Case studies and empirical evidence

Remember: You are creating a professional-grade mind map that could be used in academic papers, business presentations, or educational materials. Make it comprehensive, accurate, and visually logical.

Return the enhanced mind map as a JSON object following the specified format with layout decisions included.`;
    }

    /**
     * Validate question structure against schema
     */
    validateQuestionSchema(questions, type) {
        if (!Array.isArray(questions)) {
            throw new Error('Questions must be an array');
        }

        const requiredFields = type === 'MCQ'
            ? ['question', 'options', 'answer']
            : ['question', 'answer'];

        for (let i = 0; i < questions.length; i++) {
            const q = questions[i];

            // Check required fields
            for (const field of requiredFields) {
                if (!q.hasOwnProperty(field)) {
                    throw new Error(`Question ${i + 1} missing required field: ${field}`);
                }
            }

            // Validate MCQ specific structure
            if (type === 'MCQ') {
                if (!Array.isArray(q.options) || q.options.length < 2) {
                    throw new Error(`Question ${i + 1} must have at least 2 options`);
                }

                // Check if answer is a letter (A, B, C, D) or the actual option text
                const isLetterAnswer = /^[A-Z]$/.test(q.answer);
                const isValidLetterAnswer = isLetterAnswer && q.options.length >= (q.answer.charCodeAt(0) - 64);
                const isValidTextAnswer = !isLetterAnswer && q.options.includes(q.answer);

                if (!isValidLetterAnswer && !isValidTextAnswer) {
                    throw new Error(`Question ${i + 1} answer "${q.answer}" must be either a valid option letter (A-${String.fromCharCode(64 + q.options.length)}) or one of the option texts`);
                }
            }

            // Validate True/False specific structure
            if (type === 'TF') {
                if (typeof q.answer !== 'boolean') {
                    throw new Error(`Question ${i + 1} answer must be boolean for True/False questions`);
                }
            }
        }

        return true;
    }

    /**
     * Parse Gemini CLI response for questions with validation
     */
    parseGeminiQuestionResponse(output, type) {
        try {
            console.log('🔍 Parsing Gemini CLI question response...');

            let questions = null;

            // Look for JSON in the output
            const jsonMatch = output.match(/\[[\s\S]*\]/);
            if (jsonMatch) {
                const jsonStr = jsonMatch[0];
                questions = JSON.parse(jsonStr);

                if (Array.isArray(questions) && questions.length > 0) {
                    // Validate question structure
                    this.validateQuestionSchema(questions, type);

                    const chalk = require('chalk');
                    console.log(chalk.bgGreen.white.bold(' 🤖 AI SUCCESS ') + chalk.green.bold(` Parsed ${questions.length} questions `) + chalk.gray('from Gemini CLI'));
                }
            }

            // Fallback: try to find JSON object format
            if (!questions) {
                const objectMatch = output.match(/\{[\s\S]*\}/);
                if (objectMatch) {
                    const jsonStr = objectMatch[0];
                    const parsed = JSON.parse(jsonStr);
                    if (parsed.questions && Array.isArray(parsed.questions)) {
                        // Validate question structure
                        this.validateQuestionSchema(parsed.questions, type);

                        const chalk = require('chalk');
                        console.log(chalk.bgGreen.white.bold(' 🤖 AI SUCCESS ') + chalk.green.bold(` Parsed ${parsed.questions.length} questions `) + chalk.gray('(object format)'));
                        questions = parsed.questions;
                    }
                }
            }

            if (!questions || !Array.isArray(questions) || questions.length === 0) {
                throw new Error('No valid JSON array found in response');
            }

            // Apply smart shuffle for True/False questions to prevent predictable patterns
            if (type === 'TF' && questions.length > 2) {
                console.log('🔀 AI Agent: Applying smart shuffle to prevent T-T-T-T-F-F-F-F patterns...');
                const shuffledQuestions = this.smartShuffleTrueFalse(questions);
                console.log('✅ AI Agent: Smart shuffle applied successfully');
                return shuffledQuestions;
            }

            // Let AI handle MCQ randomization - no manual shuffling to avoid answer misalignment
            console.log('🎯 AI Agent: Using AI-generated question order for MCQ (no manual shuffle)');

            return questions;

        } catch (error) {
            console.error('Error parsing Gemini CLI question response:', error);
            console.log('Raw output:', output.substring(0, 500));
            throw new Error(`Could not parse questions from Gemini CLI response: ${error.message}`);
        }
    }

    /**
     * Classify error types for better retry logic
     */
    classifyError(errorMessage) {
        const error = errorMessage.toLowerCase();

        // Rate limiting errors
        if (error.includes('429') || error.includes('rate limit') || error.includes('quota exceeded') || error.includes('too many requests')) {
            return { isRetryable: true, type: 'rate_limit', severity: 'high' };
        }

        // Server errors
        if (error.includes('500') || error.includes('502') || error.includes('503') || error.includes('server error') || error.includes('bad gateway')) {
            return { isRetryable: true, type: 'server_error', severity: 'medium' };
        }

        // Timeout errors
        if (error.includes('timeout') || error.includes('timed out')) {
            return { isRetryable: true, type: 'timeout', severity: 'medium' };
        }

        // Process errors
        if (error.includes('process killed') || error.includes('sigterm') || error.includes('sigkill') || error.includes('failed with code null')) {
            return { isRetryable: true, type: 'process_error', severity: 'high' };
        }

        // Authentication errors (not retryable)
        if (error.includes('authentication') || error.includes('unauthorized') || error.includes('401')) {
            return { isRetryable: false, type: 'auth_error', severity: 'critical' };
        }

        // Network errors
        if (error.includes('network') || error.includes('connection') || error.includes('enotfound') || error.includes('econnreset')) {
            return { isRetryable: true, type: 'network_error', severity: 'medium' };
        }

        // Default to non-retryable
        return { isRetryable: false, type: 'unknown_error', severity: 'low' };
    }

    /**
     * Calculate exponential backoff delay based on attempt and error severity
     */
    calculateBackoffDelay(attemptCount, severity) {
        const baseDelay = 1000; // 1 second
        const maxDelay = 60000; // 60 seconds

        // Adjust multiplier based on severity
        const severityMultiplier = {
            'low': 1,
            'medium': 1.5,
            'high': 2,
            'critical': 3
        };

        const multiplier = severityMultiplier[severity] || 1;
        const exponentialDelay = baseDelay * Math.pow(2, attemptCount) * multiplier;

        // Add jitter to prevent thundering herd
        const jitter = Math.random() * 0.3 + 0.85; // 85-115% of calculated delay

        return Math.min(exponentialDelay * jitter, maxDelay);
    }

    /**
     * Parse Gemini CLI response for mind map
     */
    parseGeminiMindMapResponse(output) {
        try {
            console.log('🔍 Parsing Gemini CLI mind map response...');

            // Look for JSON object in the output
            const jsonMatch = output.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                const jsonStr = jsonMatch[0];
                const mindMap = JSON.parse(jsonStr);

                if (mindMap.title && mindMap.nodes && Array.isArray(mindMap.nodes)) {
                    console.log('✅ Successfully parsed mind map from Gemini CLI:', mindMap.title);
                    return mindMap;
                }
            }

            throw new Error('No valid mind map JSON found in response');

        } catch (error) {
            console.error('Error parsing Gemini CLI mind map response:', error);
            console.log('Raw output:', output.substring(0, 500));
            throw new Error(`Could not parse mind map from Gemini CLI response: ${error.message}`);
        }
    }



    /**
     * Smart shuffle for True/False questions to prevent predictable patterns
     * @param {Array} questions - Array of balanced TF questions
     * @returns {Array} Shuffled array with no predictable patterns
     */
    smartShuffleTrueFalse(questions) {
        if (!Array.isArray(questions) || questions.length <= 2) {
            return questions;
        }

        console.log(`🔀 Starting smart shuffle for ${questions.length} questions`);

        // Separate True and False questions
        const trueQuestions = questions.filter(q => q.answer === 'True' || q.answer === true);
        const falseQuestions = questions.filter(q => q.answer === 'False' || q.answer === false);

        console.log(`📊 Question distribution: ${trueQuestions.length} True, ${falseQuestions.length} False`);

        // Create an optimal pattern by interleaving
        const shuffled = [];
        let trueIndex = 0;
        let falseIndex = 0;

        // Create a pattern that avoids long runs
        for (let i = 0; i < questions.length; i++) {
            // Decide whether to place True or False based on what creates better distribution
            let placeTrue = false;

            if (trueIndex >= trueQuestions.length) {
                // No more True questions, must place False
                placeTrue = false;
            } else if (falseIndex >= falseQuestions.length) {
                // No more False questions, must place True
                placeTrue = true;
            } else {
                // Both available, choose based on pattern
                const lastTwo = shuffled.slice(-2).map(q => q.answer === 'True' || q.answer === true ? 'T' : 'F');

                if (lastTwo.length >= 2 && lastTwo[0] === lastTwo[1]) {
                    // Last two are the same, place opposite
                    placeTrue = lastTwo[1] !== 'T';
                } else {
                    // Random choice with slight bias toward balancing
                    const trueRatio = trueQuestions.length / questions.length;
                    const currentTrueRatio = trueIndex / (i + 1);
                    placeTrue = currentTrueRatio < trueRatio ? Math.random() > 0.3 : Math.random() > 0.7;
                }
            }

            if (placeTrue && trueIndex < trueQuestions.length) {
                shuffled.push(trueQuestions[trueIndex++]);
            } else if (falseIndex < falseQuestions.length) {
                shuffled.push(falseQuestions[falseIndex++]);
            } else {
                // Fallback
                shuffled.push(trueQuestions[trueIndex++]);
            }
        }

        this.logShufflePattern(shuffled);
        return shuffled;
    }

    /**
     * Check if the shuffled array has acceptable patterns (no long runs of same answer)
     * @param {Array} questions - Shuffled questions array
     * @returns {boolean} True if pattern is acceptable
     */
    isShuffleAcceptable(questions) {
        const answers = questions.map(q => q.answer === 'True' || q.answer === true ? 'T' : 'F');

        // Check for runs of 4 or more consecutive same answers
        let currentRun = 1;
        let maxRun = 1;

        for (let i = 1; i < answers.length; i++) {
            if (answers[i] === answers[i - 1]) {
                currentRun++;
                maxRun = Math.max(maxRun, currentRun);
            } else {
                currentRun = 1;
            }
        }

        // Reject if there are 3+ consecutive same answers (changed from 4+ to 3+)
        if (maxRun >= 3) {
            return false;
        }

        // Check for alternating patterns (T-F-T-F-T-F...)
        let alternatingCount = 0;
        for (let i = 1; i < Math.min(10, answers.length); i++) {
            if (answers[i] !== answers[i - 1]) {
                alternatingCount++;
            }
        }

        // Reject if first 10 questions are mostly alternating (8+ out of 9 transitions)
        if (answers.length >= 10 && alternatingCount >= 8) {
            return false;
        }

        return true;
    }

    /**
     * Manually break predictable patterns when random shuffle fails
     * @param {Array} questions - Questions array with bad patterns
     * @returns {Array} Questions with broken patterns
     */
    breakPredictablePatterns(questions) {
        const shuffled = [...questions];
        const answers = shuffled.map(q => q.answer === 'True' || q.answer === true ? 'T' : 'F');

        // Break long runs by swapping elements (changed to break runs of 3+)
        for (let i = 0; i < answers.length - 2; i++) {
            if (answers[i] === answers[i + 1] && answers[i + 1] === answers[i + 2]) {
                // Found 3 consecutive same answers, find a different answer to swap with
                const targetAnswer = answers[i] === 'T' ? 'F' : 'T';

                for (let j = i + 3; j < answers.length; j++) {
                    if (answers[j] === targetAnswer) {
                        // Swap questions
                        [shuffled[i + 1], shuffled[j]] = [shuffled[j], shuffled[i + 1]];
                        answers[i + 1] = targetAnswer;
                        answers[j] = answers[i];
                        break;
                    }
                }
            }
        }

        return shuffled;
    }

    /**
     * Log the shuffle pattern for debugging
     * @param {Array} questions - Shuffled questions
     */
    logShufflePattern(questions) {
        const pattern = questions.slice(0, 20).map(q => q.answer === 'True' || q.answer === true ? 'T' : 'F').join('');
        console.log(`🎯 Shuffle pattern (first 20): ${pattern}`);

        // Count runs
        const answers = questions.map(q => q.answer === 'True' || q.answer === true ? 'T' : 'F');
        let runs = [];
        let currentRun = { answer: answers[0], length: 1 };

        for (let i = 1; i < answers.length; i++) {
            if (answers[i] === currentRun.answer) {
                currentRun.length++;
            } else {
                runs.push(currentRun);
                currentRun = { answer: answers[i], length: 1 };
            }
        }
        runs.push(currentRun);

        const maxRun = Math.max(...runs.map(r => r.length));
        console.log(`📊 Longest run: ${maxRun} consecutive answers`);
    }

    /**
     * Update environment variables in .env file
     */
    async updateEnvFile(config) {
        try {
            const envPath = path.join(__dirname, '../.env');
            let envContent = '';

            // Read existing .env file
            if (fs.existsSync(envPath)) {
                envContent = fs.readFileSync(envPath, 'utf8');
            }

            // Update or add configuration values
            const updateEnvVar = (key, value) => {
                const regex = new RegExp(`^${key}=.*$`, 'm');
                const newLine = `${key}=${value}`;

                if (regex.test(envContent)) {
                    envContent = envContent.replace(regex, newLine);
                } else {
                    envContent += `\n${newLine}`;
                }
            };

            if (config.botToken) {
                updateEnvVar('TELEGRAM_BOT_TOKEN', config.botToken);
            }

            if (config.adminIds && Array.isArray(config.adminIds)) {
                updateEnvVar('ADMIN_IDS', config.adminIds.join(','));
            }

            if (config.requiredChannelId) {
                updateEnvVar('REQUIRED_CHANNEL_ID', config.requiredChannelId);
            }

            if (config.requiredChannelId) {
                updateEnvVar('REQUIRED_CHANNEL_ID', config.requiredChannelId);
            }

            if (config.apiKey) {
                updateEnvVar('API_KEY', config.apiKey);
            }

            // Write updated content back to file
            fs.writeFileSync(envPath, envContent.trim() + '\n');

        } catch (error) {
            logger.error('Error updating .env file:', error);
            throw error;
        }
    }

    /**
     * Get comprehensive user statistics (async version)
     */
    async getUserStatisticsAsync(user) {
        try {
            const fs = require('fs');
            const path = require('path');
            const username = user.username || `user_${user.id}`;

            let stats = {
                savedQuizzes: 0,
                questionsGenerated: 0,
                quizzesTaken: 0,
                filesUploaded: 0
            };

            // 1. Get saved quizzes count
            const savedQuizzesDir = path.join(__dirname, 'data/saved_quizzes');
            const userQuizzesFile = path.join(savedQuizzesDir, `${username}_quizzes.json`);

            if (fs.existsSync(userQuizzesFile)) {
                const data = fs.readFileSync(userQuizzesFile, 'utf8');
                const quizzes = JSON.parse(data);
                stats.savedQuizzes = Array.isArray(quizzes) ? quizzes.length : 0;
            }

            // 2. Get database statistics
            const db = database.db();
            if (db) {
                try {
                    // Get quiz attempts (quizzes taken)
                    const quizAttemptsCount = await new Promise((resolve) => {
                        db.get('SELECT COUNT(*) as count FROM quiz_attempts WHERE user_id = ? OR username = ?',
                            [user.id, username], (err, row) => {
                                if (err) {
                                    console.log(`Error getting quiz attempts for ${username}:`, err.message);
                                    resolve(0);
                                } else {
                                    resolve(row ? row.count : 0);
                                }
                            });
                    });
                    stats.quizzesTaken = quizAttemptsCount;

                    // Get quiz sessions (questions generated) - sum total questions, not count sessions
                    const questionsCount = await new Promise((resolve) => {
                        db.get('SELECT SUM(questions_answered) as total FROM quiz_sessions WHERE user_id = ?',
                            [user.id], (err, row) => {
                                if (err) {
                                    console.log(`Error getting quiz sessions for ${username}:`, err.message);
                                    resolve(0);
                                } else {
                                    resolve(row ? (row.total || 0) : 0);
                                }
                            });
                    });
                    stats.questionsGenerated = questionsCount;

                    // Get file uploads
                    const filesCount = await new Promise((resolve) => {
                        db.get('SELECT count FROM file_uploads WHERE user_id = ?',
                            [user.id.toString()], (err, row) => {
                                if (err) {
                                    console.log(`Error getting file uploads for ${username}:`, err.message);
                                    resolve(0);
                                } else {
                                    resolve(row ? row.count : 0);
                                }
                            });
                    });
                    stats.filesUploaded = filesCount;

                } catch (dbError) {
                    console.log(`Error querying database for user ${username}:`, dbError.message);
                }
            }

            return stats;
        } catch (error) {
            console.log(`Error getting user statistics for ${user.username || user.id}:`, error.message);
            return {
                savedQuizzes: 0,
                questionsGenerated: 0,
                quizzesTaken: 0,
                filesUploaded: 0
            };
        }
    }

    /**
     * Get comprehensive user statistics (sync version for backward compatibility)
     */
    getUserStatistics(user) {
        try {
            const fs = require('fs');
            const path = require('path');
            const username = user.username || `user_${user.id}`;

            let stats = {
                savedQuizzes: 0,
                questionsGenerated: 0,
                quizzesTaken: 0,
                filesUploaded: 0
            };

            // 1. Get saved quizzes count
            const savedQuizzesDir = path.join(__dirname, 'data/saved_quizzes');
            const userQuizzesFile = path.join(savedQuizzesDir, `${username}_quizzes.json`);

            if (fs.existsSync(userQuizzesFile)) {
                const data = fs.readFileSync(userQuizzesFile, 'utf8');
                const quizzes = JSON.parse(data);
                stats.savedQuizzes = Array.isArray(quizzes) ? quizzes.length : 0;
            }

            // For now, only return file-based statistics in sync version
            // Database statistics require async handling
            return stats;
        } catch (error) {
            console.log(`Error getting user statistics for ${user.username || user.id}:`, error.message);
            return {
                savedQuizzes: 0,
                questionsGenerated: 0,
                quizzesTaken: 0,
                filesUploaded: 0
            };
        }
    }

    /**
     * Get quiz count for a specific user (backward compatibility)
     */
    getUserQuizCount(username) {
        // This method is kept for backward compatibility
        // It now returns saved quizzes count only
        try {
            const fs = require('fs');
            const path = require('path');

            const savedQuizzesDir = path.join(__dirname, 'data/saved_quizzes');
            const userQuizzesFile = path.join(savedQuizzesDir, `${username}_quizzes.json`);

            if (fs.existsSync(userQuizzesFile)) {
                const data = fs.readFileSync(userQuizzesFile, 'utf8');
                const quizzes = JSON.parse(data);
                return Array.isArray(quizzes) ? quizzes.length : 0;
            }

            return 0;
        } catch (error) {
            console.log(`Error getting saved quiz count for ${username}:`, error.message);
            return 0;
        }
    }
}

module.exports = IPCHandlers;
