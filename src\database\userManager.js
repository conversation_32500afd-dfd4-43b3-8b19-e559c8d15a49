const fs = require('fs').promises;
const path = require('path');
const logger = require('../utils/logger');

class UserManager {
    constructor() {
        this.dbPath = path.join(__dirname, 'users.json');
        this.users = new Map();
        this.initialized = false;
        logger.debug('Database path:', this.dbPath);
    }

    // Singleton pattern for global access
    static getInstance() {
        if (!UserManager.instance) {
            UserManager.instance = new UserManager();
        }
        return UserManager.instance;
    }

    async initialize() {
        if (this.initialized) return;

        try {
            await this.loadUsers();

            // Clear any sample/mock data and keep only real Telegram users
            await this.cleanupSampleData();

            this.initialized = true;
            logger.success('UserManager initialized successfully');
        } catch (error) {
            logger.error('Failed to initialize UserManager:', error);
            // Create empty database if it doesn't exist
            await this.saveUsers();
            this.initialized = true;
        }
    }

    // Remove sample data and admin accounts, keep only real Telegram users
    async cleanupSampleData() {
        const sampleUserIds = ['*********', '*********', '*********']; // Known sample IDs
        const adminUsernames = ['akai']; // Admin usernames to exclude
        let removedCount = 0;

        // Remove known sample users
        for (const sampleId of sampleUserIds) {
            if (this.users.has(sampleId)) {
                this.users.delete(sampleId);
                removedCount++;
            }
        }

        // Remove admin accounts that shouldn't be in Telegram user list
        for (const [userId, user] of this.users.entries()) {
            if (adminUsernames.includes(user.username) || adminUsernames.includes(user.displayName)) {
                this.users.delete(userId);
                removedCount++;
                logger.info(`Removed admin account "${user.displayName}" from Telegram users`);
            }
        }

        if (removedCount > 0) {
            await this.saveUsers();
            logger.info(`Removed ${removedCount} non-Telegram users, keeping only real Telegram bot users`);
        }
    }

    async loadUsers() {
        try {
            logger.debug('Attempting to load users from:', this.dbPath);
            const data = await fs.readFile(this.dbPath, 'utf8');
            const usersArray = JSON.parse(data);

            this.users.clear();
            usersArray.forEach(user => {
                // Convert date strings back to Date objects
                if (user.joinedAt) user.joinedAt = new Date(user.joinedAt);
                if (user.expiresAt) user.expiresAt = new Date(user.expiresAt);
                if (user.lastActivity) user.lastActivity = new Date(user.lastActivity);

                this.users.set(user.telegramId, user);
            });

            // Only log if user count changed or it's been more than 2 minutes
            if (!this.lastUserCountLog || this.lastUserCount !== this.users.size ||
                (Date.now() - this.lastUserCountLog) > 120000) {
                logger.info(`Loaded ${this.users.size} users from database`);
                this.lastUserCount = this.users.size;
                this.lastUserCountLog = Date.now();
            }
        } catch (error) {
            if (error.code === 'ENOENT') {
                // File doesn't exist, create with sample data
                logger.info('Creating new user database with sample data');
                await this.createSampleData();
            } else {
                logger.error('Error loading users:', error);
                throw error;
            }
        }
    }

    async createSampleData() {
        // Don't create sample data - wait for real Telegram users
        logger.info('No sample data created - waiting for real Telegram users');
    }

    async saveUsers() {
        try {
            const usersArray = Array.from(this.users.values());
            await fs.writeFile(this.dbPath, JSON.stringify(usersArray, null, 2));
            logger.debug(`Saved ${usersArray.length} users to database`);
        } catch (error) {
            logger.error('Failed to save users:', error);
            throw error;
        }
    }

    // Add or update user
    async addUser(telegramUser) {
        const userId = telegramUser.id.toString();
        const existingUser = this.users.get(userId);
        
        const user = {
            telegramId: userId,
            username: telegramUser.username ? `@${telegramUser.username}` : null,
            firstName: telegramUser.first_name || '',
            lastName: telegramUser.last_name || '',
            displayName: this.getDisplayName(telegramUser),
            type: existingUser ? existingUser.type : 'free',
            subscriptionType: existingUser ? existingUser.subscriptionType : null,
            expiresAt: existingUser ? existingUser.expiresAt : null,
            joinedAt: existingUser ? existingUser.joinedAt : new Date(),
            lastActivity: new Date(),
            isActive: true,
            questionsGenerated: existingUser ? existingUser.questionsGenerated : 0,
            filesProcessed: existingUser ? existingUser.filesProcessed : 0,
            // Rate limiting data
            dailyFilesUsed: existingUser ? existingUser.dailyFilesUsed : 0,
            lastFileDate: existingUser ? existingUser.lastFileDate : null,
            lastQuestionGeneration: existingUser ? existingUser.lastQuestionGeneration : null
        };

        this.users.set(userId, user);
        await this.saveUsers();
        
        return user;
    }

    // Get user by Telegram ID
    getUser(telegramId) {
        return this.users.get(telegramId.toString());
    }

    // Get all users
    getAllUsers() {
        return Array.from(this.users.values());
    }

    // Update user subscription
    async updateSubscription(telegramId, subscriptionType, days) {
        const user = this.users.get(telegramId.toString());
        if (!user) throw new Error('User not found');

        const now = new Date();
        const expiresAt = new Date(now.getTime() + days * 24 * 60 * 60 * 1000);

        user.type = 'paid';
        user.subscriptionType = subscriptionType;
        user.expiresAt = expiresAt;

        await this.saveUsers();
        return user;
    }

    // Check if user subscription is expired
    isSubscriptionExpired(user) {
        if (user.type !== 'paid' || !user.expiresAt) return false;
        return new Date() > user.expiresAt;
    }

    // Update expired users
    async updateExpiredUsers() {
        let updated = false;
        
        for (const user of this.users.values()) {
            if (this.isSubscriptionExpired(user)) {
                user.type = 'expired';
                updated = true;
            }
        }

        if (updated) {
            await this.saveUsers();
        }

        return updated;
    }

    // Get user statistics
    getStatistics() {
        const users = this.getAllUsers();
        
        return {
            total: users.length,
            free: users.filter(u => u.type === 'free').length,
            paid: users.filter(u => u.type === 'paid').length,
            expired: users.filter(u => u.type === 'expired').length,
            active: users.filter(u => {
                const daysSinceActivity = (new Date() - u.lastActivity) / (1000 * 60 * 60 * 24);
                return daysSinceActivity <= 7; // Active in last 7 days
            }).length
        };
    }

    // Search users
    searchUsers(query, filter = 'all') {
        const users = this.getAllUsers();
        const searchTerm = query.toLowerCase().replace('@', ''); // Remove @ from search term

        return users.filter(user => {
            // Apply type filter
            if (filter !== 'all' && user.type !== filter) return false;

            // Apply search query
            if (!query) return true;

            return (
                user.displayName.toLowerCase().replace('@', '').includes(searchTerm) ||
                (user.username && user.username.toLowerCase().replace('@', '').includes(searchTerm)) ||
                user.telegramId.includes(searchTerm) ||
                user.firstName.toLowerCase().includes(searchTerm) ||
                user.lastName.toLowerCase().includes(searchTerm)
            );
        });
    }

    // Remove user
    async removeUser(telegramId) {
        const deleted = this.users.delete(telegramId.toString());
        if (deleted) {
            await this.saveUsers();
        }
        return deleted;
    }

    // Add or update user from Telegram bot
    async addOrUpdateUser(telegramUser) {
        try {
            // Validate that this is a real Telegram user with numeric ID
            if (!telegramUser.id || isNaN(telegramUser.id)) {
                logger.warn(`Invalid Telegram user ID: ${telegramUser.id}, skipping`);
                return null;
            }

            const telegramId = telegramUser.id.toString();

            // Additional validation - Telegram IDs are typically large numbers
            if (telegramId.length < 6) {
                logger.warn(`Suspicious Telegram ID (too short): ${telegramId}, skipping`);
                return null;
            }

            const existingUser = this.users.get(telegramId);

            // Create clean display name from real Telegram data
            let displayName = '';
            if (telegramUser.username) {
                displayName = telegramUser.username;
            } else if (telegramUser.first_name || telegramUser.last_name) {
                displayName = [telegramUser.first_name, telegramUser.last_name].filter(Boolean).join(' ');
            } else {
                displayName = `User ${telegramId}`;
            }

            const userData = {
                telegramId: telegramId,
                username: telegramUser.username || null,
                firstName: telegramUser.first_name || '',
                lastName: telegramUser.last_name || '',
                displayName: displayName,
                type: existingUser ? existingUser.type : 'free', // Keep existing type or default to free
                subscriptionType: existingUser ? existingUser.subscriptionType : null,
                expiresAt: existingUser ? existingUser.expiresAt : null,
                joinedAt: existingUser ? existingUser.joinedAt : new Date(),
                lastActivity: new Date(),
                isActive: true,
                questionsGenerated: existingUser ? existingUser.questionsGenerated : 0,
                filesProcessed: existingUser ? existingUser.filesProcessed : 0,
                // Add Telegram-specific data
                languageCode: telegramUser.language_code || 'en',
                isBot: telegramUser.is_bot || false
            };

            this.users.set(telegramId, userData);
            await this.saveUsers();

            logger.user(`Real Telegram user ${telegramId} (${userData.displayName}) added/updated`);
            return userData;

        } catch (error) {
            logger.error('Error adding/updating Telegram user:', error);
            throw error;
        }
    }

    // Helper method to get display name
    getDisplayName(telegramUser) {
        if (telegramUser.username) {
            // Don't add @ symbol, just use the username as is
            return telegramUser.username;
        }

        const parts = [];
        if (telegramUser.first_name) parts.push(telegramUser.first_name);
        if (telegramUser.last_name) parts.push(telegramUser.last_name);

        return parts.length > 0 ? parts.join(' ') : `User ${telegramUser.id}`;
    }

    // Increment user activity counters
    async incrementQuestions(telegramId) {
        const user = this.users.get(telegramId.toString());
        if (user) {
            user.questionsGenerated = (user.questionsGenerated || 0) + 1;
            user.lastActivity = new Date();
            await this.saveUsers();
        }
    }

    async incrementFiles(telegramId) {
        const user = this.users.get(telegramId.toString());
        if (user) {
            user.filesProcessed = (user.filesProcessed || 0) + 1;
            user.lastActivity = new Date();
            await this.saveUsers();
        }
    }

    // Export users to CSV
    exportToCSV() {
        const users = this.getAllUsers();
        const headers = [
            'Telegram ID',
            'Username',
            'Display Name',
            'First Name',
            'Last Name',
            'Type',
            'Subscription',
            'Expires At',
            'Joined At',
            'Last Activity',
            'Questions Generated',
            'Files Processed'
        ];

        const csvRows = [headers.join(',')];
        
        users.forEach(user => {
            const row = [
                user.telegramId,
                user.username || '',
                `"${user.displayName}"`,
                `"${user.firstName}"`,
                `"${user.lastName}"`,
                user.type,
                user.subscriptionType || '',
                user.expiresAt ? user.expiresAt.toISOString() : '',
                user.joinedAt.toISOString(),
                user.lastActivity.toISOString(),
                user.questionsGenerated || 0,
                user.filesProcessed || 0
            ];
            csvRows.push(row.join(','));
        });

        return csvRows.join('\n');
    }
}

// Export the class with singleton support
module.exports = UserManager;
